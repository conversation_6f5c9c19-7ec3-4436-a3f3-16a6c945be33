# Набор тестов для вызова инструментов LLM V2

Комплексный CLI инструмент для тестирования возможностей вызова функций у LLM через стандарт OpenAI API, со специальной поддержкой маршрутизации провайдеров OpenRouter.

## ВАЖНО
- Семантическая точность пока слишком рано использовать как индикатор
- Структурная точность и полнота инструментов - основные метрики для анализа

### Пример вывода

```
Успешность сценариев: 44.4%
├─ Легкий индикатор, чем выше, тем лучше

Точность инструментов (только LLM): 46.1%
├─ Мера того, как часто вызывается правильный инструмент
├─ Чем выше, тем лучше, хороший индикатор с текущими реализациями

Полнота инструментов (только LLM): 54.2%
├─ Ожидаемые инструменты против фактически вызванных
├─ Чем выше, тем лучше, хороший индикатор

F1 инструментов (только LLM): 49.8%
├─ Комбинация точности и полноты

Точность параметров (структурная): 100.0%
├─ Форма вызовов инструментов корректна

Точность параметров (только LLM): 100.0%
├─ Будущая функция - разделение эмулированных вызовов (можно игнорировать)

Точность параметров (семантическая): 46.1%
├─ Пока слишком рано использовать как индикатор, но может быть точкой данных
├─ Проверяет фактические значения, переданные в инструмент

Точность параметров (семантическая, только LLM): 46.1%
├─ Будущая функция - разделение эмулированных вызовов (можно игнорировать)

Успешность выполнения: 100.0%
Успешность выполнения (только LLM): 100.0%

Взвешенная оценка сложности: 46.6%
├─ Зависит от сложности сценария
├─ Чем выше, тем лучше

═══════════════════════════════════════════════════════════════
ОБЩАЯ ОЦЕНКА: 65.2% (B-)
├─ Используйте как общий индикатор
└─ Обращайте внимание на нюансы, так как каждое значение важно по-разному
```


## Возможности

- Сценарии вызова инструментов на основе естественного диалога
- Тесты от простых (1-5 инструментов) до сложных (40+ инструментов) сценариев
- Поддержка любых OpenAI-совместимых API эндпоинтов, включая OpenRouter
- Специфическая маршрутизация провайдеров для оптимальной производительности
- Детальные метрики: точность, полнота, F1 оценки и семантическая точность
- Автоматическое определение возможностей для различных серверных реализаций
- Скрипты пакетного тестирования для сравнения моделей и провайдеров

## Установка

```bash
pip install -r requirements.txt
```

## Использование

### Базовое использование

```bash
python tool_tester_v2.py --api-base <API_URL> --api-key <API_KEY> --model <MODEL_NAME>
```

### Примеры

Тестирование OpenAI GPT-4:
```bash
python tool_tester_v2.py --api-base https://api.openai.com/v1 --api-key sk-xxx --model gpt-4o
```

Тестирование через OpenRouter с маршрутизацией провайдера:
```bash
python tool_tester_v2.py --api-base https://openrouter.ai/api/v1 --api-key sk-or-xxx --model anthropic/claude-3.5-sonnet --provider Anthropic
```

Тестирование локальной модели:
```bash
python tool_tester_v2.py --api-base http://localhost:8000/v1 --api-key local --model llama-70b
```

Режим быстрого тестирования (быстрее, меньше сценариев):
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --quick
```

Сохранение отчета в файл:
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --output report.txt
```

Ограничение максимального количества вызовов инструментов:
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --max-tools 20
```

### Использование скрипта общего пакетного тестирования

Скрипт `test_generic_model.sh` позволяет легко тестировать любую модель на нескольких провайдерах:

1. **Отредактируйте конфигурацию скрипта** в верхней части файла:
   ```bash
   # Отредактируйте эти переменные в test_generic_model.sh:
   MODEL="openai/gpt-4o"           # Ваша модель
   TEMPERATURE=0.1                  # Настройка температуры
   providers=(                      # Список провайдеров для тестирования
       "openai"
       "anthropic"
       "fireworks"
   )
   ```

2. **Запустите скрипт**:
   ```bash
   # Быстрые тесты (меньше сценариев)
   ./test_generic_model.sh YOUR_API_KEY quick

   # Полный набор тестов
   ./test_generic_model.sh YOUR_API_KEY full
   ```

3. **Результаты** будут сохранены в директории с временной меткой:
   - Индивидуальные результаты тестов для каждого провайдера
   - Сводный отчет с оценками
   - CSV файл для удобного анализа
   - Отсортированная таблица лидеров провайдеров по производительности

### Тестирование специфичных провайдеров

Некоторые примеры конфигураций провайдеров:
```bash
# Тестирование с определенными режимами точности
--provider "fireworks/fp8"
--provider "deepinfra/fp4"
--provider "together/fp8"

# Тестирование с усилием рассуждения (для поддерживаемых моделей)
--reasoning-effort high

# Настройка температуры для тестирования
--temperature 0.0  # Наиболее детерминистично
--temperature 0.7  # Более креативно
```

## Категории тестов

1. **Простые тесты (1-5 инструментов)**: Базовые сценарии с одним и несколькими инструментами
2. **Средние тесты (6-10 инструментов)**: Умеренная сложность с последующими действиями
3. **Большие тесты (11-20 инструментов)**: Сложные многошаговые операции
4. **Очень большие тесты (21+ инструментов)**: Комплексные симуляции рабочих процессов

## Доступные инструменты

Набор тестов включает 10 различных типов инструментов:
- `get_weather`: Информация о погоде для локаций
- `calculate`: Математические вычисления
- `search_flights`: Поиск рейсов между городами
- `search_hotels`: Поиск доступности отелей
- `search_restaurants`: Рекомендации ресторанов
- `translate_text`: Языковой перевод
- `get_stock_price`: Цены на фондовом рынке
- `convert_currency`: Конвертация валют
- `get_news`: Новостные статьи по темам
- `set_reminder`: Создание напоминаний

## Объяснение метрик

- **Успешность сценариев**: Процент успешно завершенных тестовых сценариев
- **Точность инструментов**: Когда ИИ вызывает инструмент, как часто это правильный
- **Полнота инструментов**: Процент необходимых инструментов, которые были фактически вызваны
- **F1 оценка инструментов**: Гармоническое среднее точности и полноты
- **Точность параметров (структурная)**: Правильный формат и обязательные поля
- **Точность параметров (семантическая)**: Правильные значения для контекста
- **Успешность выполнения**: Инструменты, которые выполнились без ошибок
- **Взвешенная оценка сложности**: Производительность, скорректированная на сложность сценария

## Критерии успеха

- Индивидуальные тесты проходят, если ≥70% ожидаемых инструментов вызваны правильно
- Структурные параметры должны быть ≥60% правильными
- Общая оценка основана на комбинированных метриках (A+ = 90%+, A = 85%+, и т.д.)

## Вывод

Инструмент генерирует комплексный отчет, показывающий:
- Общую успешность и буквенную оценку
- Детальные метрики (точность, полнота, F1)
- Разбивку по сценариям
- Детали вызовов инструментов и статистику эмуляции
- Время выполнения
- Детали ошибок и логи разговоров

## Тестовые сценарии

Тестовые сценарии определены в `test_scenarios.json` и включают:
- Планирование путешествий
- Инвестиционные исследования
- Координация мероприятий
- Академические конференции
- Торговые экспедиции
- И множество других реальных случаев использования