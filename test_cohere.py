#!/usr/bin/env python3
"""
Простой тест Cohere API для проверки базовой функциональности
"""

import cohere

def test_cohere_basic():
    """Базовый тест Cohere API"""
    
    # Инициализация клиента
    co = cohere.ClientV2(api_key="FEBYH8H2ogbtTRaBmChNCaz3aMnmE3EtBkNQWYv5")
    
    print("🚀 Тестирование Cohere API...")
    print("=" * 50)
    
    try:
        # Тест базового чата
        print("📝 Тест 1: Базовый чат")
        response = co.chat(
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Привет! Как дела?"
                        }
                    ]
                }
            ],
            temperature=0.3,
            model="command-a-03-2025",
        )
        
        print(f"✅ Ответ получен:")
        print(f"   Модель: command-a-03-2025")

        # Отладочная информация
        print(f"   Тип ответа: {type(response)}")
        print(f"   Атрибуты: {dir(response)}")

        if hasattr(response, 'message') and hasattr(response.message, 'content'):
            for content in response.message.content:
                if hasattr(content, 'type') and content.type == 'text':
                    print(f"   Текст: {content.text}")
        else:
            print(f"   Полный ответ: {response}")
        
        print("\n" + "=" * 50)
        
        # Тест с историей диалога
        print("📝 Тест 2: Диалог с историей")
        response2 = co.chat(
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Привет"
                        }
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {
                            "type": "text",
                            "text": "Привет! Как дела? Чем могу помочь?"
                        }
                    ]
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Расскажи короткую шутку"
                        }
                    ]
                }
            ],
            temperature=0.7,
            model="command-a-03-2025",
        )
        
        print(f"✅ Ответ с историей получен:")
        if hasattr(response2, 'message') and hasattr(response2.message, 'content'):
            for content in response2.message.content:
                if hasattr(content, 'type') and content.type == 'text':
                    print(f"   Шутка: {content.text}")
        else:
            print(f"   Полный ответ: {response2}")
            
        print("\n" + "=" * 50)
        
        # Тест технических возможностей
        print("📝 Тест 3: Технические возможности")
        response3 = co.chat(
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Поддерживаешь ли ты вызов функций (function calling/tool calling)?"
                        }
                    ]
                }
            ],
            temperature=0.1,
            model="command-a-03-2025",
        )
        
        print(f"✅ Ответ о возможностях:")
        if hasattr(response3, 'message') and hasattr(response3.message, 'content'):
            for content in response3.message.content:
                if hasattr(content, 'type') and content.type == 'text':
                    print(f"   Ответ: {content.text}")
        else:
            print(f"   Полный ответ: {response3}")
            
        print("\n" + "🎉 Все тесты успешно выполнены!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")
        return False
    
    return True

def test_cohere_models():
    """Тест доступных моделей"""
    
    co = cohere.ClientV2(api_key="FEBYH8H2ogbtTRaBmChNCaz3aMnmE3EtBkNQWYv5")
    
    print("\n🔍 Тестирование разных моделей Cohere...")
    print("=" * 50)
    
    models_to_test = [
        "command-a-03-2025",
    ]
    
    for model in models_to_test:
        try:
            print(f"📝 Тестирование модели: {model}")
            response = co.chat(
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Скажи 'Привет' и назови свою модель"
                            }
                        ]
                    }
                ],
                temperature=0.1,
                model=model,
            )
            
            if hasattr(response, 'message') and hasattr(response.message, 'content'):
                for content in response.message.content:
                    if hasattr(content, 'type') and content.type == 'text':
                        print(f"   ✅ {model}: {content.text[:100]}...")
            else:
                print(f"   ✅ {model}: Ответ получен")
                
        except Exception as e:
            print(f"   ❌ {model}: Ошибка - {e}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print("🧪 Cohere API Test Suite")
    print("=" * 50)
    
    # Основные тесты
    success = test_cohere_basic()
    
    if success:
        # Тест моделей
        test_cohere_models()
        
        print("\n🎯 Рекомендации:")
        print("   1. API работает корректно")
        print("   2. Можно интегрировать в tool calling тестер")
        print("   3. Попробуйте разные модели для оптимальной производительности")
    else:
        print("\n⚠️  Проблемы с API:")
        print("   1. Проверьте API ключ")
        print("   2. Проверьте подключение к интернету")
        print("   3. Убедитесь, что установлен пакет cohere")
        print("   4. Команда установки: pip install cohere")
