"""
Tool Definitions for LLM Tool Calling Test Suite

This module contains all tool definitions and related functionality
for testing LLM tool calling capabilities.
"""

from typing import Dict, List, Any


class ToolDefinitions:
    """Define available tools for testing"""

    @staticmethod
    def get_all_tools() -> List[Dict[str, Any]]:
        return [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get the current weather for a specific location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "City and state, e.g. San Francisco, CA"},
                            "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "Temperature unit"}
                        },
                        "required": ["location"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "calculate",
                    "description": "Perform mathematical calculations",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {"type": "string", "description": "Mathematical expression to evaluate"}
                        },
                        "required": ["expression"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_flights",
                    "description": "Search for available flights between two cities",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "from_city": {"type": "string", "description": "Departure city"},
                            "to_city": {"type": "string", "description": "Destination city"},
                            "date": {"type": "string", "description": "Travel date (YYYY-MM-DD)"}
                        },
                        "required": ["from_city", "to_city", "date"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_stock_price",
                    "description": "Get current stock price for a company",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "symbol": {"type": "string", "description": "Stock ticker symbol"}
                        },
                        "required": ["symbol"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_restaurants",
                    "description": "Search for restaurants in a specific area",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "City or area to search"},
                            "cuisine": {"type": "string", "description": "Type of cuisine"},
                            "price_range": {"type": "string", "enum": ["$", "$$", "$$$", "$$$$"]}
                        },
                        "required": ["location"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "convert_currency",
                    "description": "Convert amount between two currencies",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "amount": {"type": "number", "description": "Amount to convert"},
                            "from_currency": {"type": "string", "description": "Source currency code (e.g., USD)"},
                            "to_currency": {"type": "string", "description": "Target currency code (e.g., EUR)"}
                        },
                        "required": ["amount", "from_currency", "to_currency"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_news",
                    "description": "Get latest news articles on a topic",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "topic": {"type": "string", "description": "News topic or search query"},
                            "limit": {"type": "integer", "description": "Number of articles to return", "default": 5}
                        },
                        "required": ["topic"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "set_reminder",
                    "description": "Set a reminder for a specific time",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "message": {"type": "string", "description": "Reminder message"},
                            "time": {"type": "string", "description": "Time for the reminder (HH:MM)"},
                            "date": {"type": "string", "description": "Date for the reminder (YYYY-MM-DD)"}
                        },
                        "required": ["message", "time"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "translate_text",
                    "description": "Translate text from one language to another",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string", "description": "Text to translate"},
                            "source_language": {"type": "string", "description": "Source language code"},
                            "target_language": {"type": "string", "description": "Target language code"}
                        },
                        "required": ["text", "target_language"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_hotels",
                    "description": "Search for hotels in a specific location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "City or area"},
                            "check_in": {"type": "string", "description": "Check-in date (YYYY-MM-DD)"},
                            "check_out": {"type": "string", "description": "Check-out date (YYYY-MM-DD)"},
                            "guests": {"type": "integer", "description": "Number of guests"}
                        },
                        "required": ["location", "check_in", "check_out"]
                    }
                }
            }
        ]

    @staticmethod
    def _allowed_props() -> Dict[str, set]:
        return {
            "get_weather": {"location", "unit"},
            "calculate": {"expression"},
            "search_flights": {"from_city", "to_city", "date"},
            "get_stock_price": {"symbol"},
            "search_restaurants": {"location", "cuisine", "price_range"},
            "convert_currency": {"amount", "from_currency", "to_currency"},
            "get_news": {"topic", "limit"},
            "set_reminder": {"message", "time", "date"},
            "translate_text": {"text", "source_language", "target_language"},
            "search_hotels": {"location", "check_in", "check_out", "guests"},
        }

    @staticmethod
    def validate_parameters(tool_name: str, arguments: Dict[str, Any]) -> bool:
        """Validate required parameters and reject unknown keys"""
        allowed = ToolDefinitions._allowed_props().get(tool_name, set())
        if any(k not in allowed for k in arguments.keys()):
            return False

        validations = {
            "get_weather": lambda args: "location" in args and len(str(args.get("location", "")).strip()) > 0,
            "calculate": lambda args: "expression" in args and len(str(args.get("expression", "")).strip()) > 0,
            "search_flights": lambda args: all(k in args for k in ["from_city", "to_city", "date"]) and
                                           all(len(str(args.get(k, "")).strip()) > 0 for k in ["from_city", "to_city", "date"]),
            "get_stock_price": lambda args: "symbol" in args and len(str(args.get("symbol", "")).strip()) > 0,
            "search_restaurants": lambda args: "location" in args and len(str(args.get("location", "")).strip()) > 0,
            "convert_currency": lambda args: all(k in args for k in ["amount", "from_currency", "to_currency"]) and
                                             isinstance(args.get("amount", 0), (int, float)) and args.get("amount", 0) > 0,
            "get_news": lambda args: "topic" in args and len(str(args.get("topic", "")).strip()) > 0,
            "set_reminder": lambda args: all(k in args for k in ["message", "time"]) and
                                         all(len(str(args.get(k, "")).strip()) > 0 for k in ["message", "time"]),
            "translate_text": lambda args: "text" in args and "target_language" in args and
                                           all(len(str(args.get(k, "")).strip()) > 0 for k in ["text", "target_language"]),
            "search_hotels": lambda args: all(k in args for k in ["location", "check_in", "check_out"]) and
                                          all(len(str(args.get(k, "")).strip()) > 0 for k in ["location", "check_in", "check_out"]),
        }
        validator = validations.get(tool_name)
        if validator:
            try:
                return validator(arguments)
            except Exception:
                return False
        return True

    @staticmethod
    def execute_tool(name: str, arguments: Dict[str, Any]) -> str:
        """Mock tool execution - returns realistic dummy data"""

        tool_responses = {
            "get_weather": lambda args: f"The weather in {args.get('location', 'Unknown')} is currently 72°F (22°C) with partly cloudy skies. Humidity is 65% with winds at 10 mph.",

            "calculate": lambda args: f"The result of {args.get('expression', '')} is {eval(args.get('expression', '0'))}",

            "search_flights": lambda args: (
                f"Found 5 flights from {args.get('from_city')} to {args.get('to_city')} on {args.get('date')}:\n"
                "1. UA 245 - Departs 8:00 AM, arrives 11:30 AM - $350\n"
                "2. DL 892 - Departs 10:15 AM, arrives 1:45 PM - $425\n"
                "3. AA 156 - Departs 2:30 PM, arrives 6:00 PM - $380"
            ),

            "get_stock_price": lambda args: f"{args.get('symbol', 'UNKNOWN')} is currently trading at $152.35, up 2.3% today. Day range: $149.20 - $153.80",

            "search_restaurants": lambda args: (
                f"Found 3 top restaurants in {args.get('location')}:\n"
                f"1. The Golden Fork - {args.get('cuisine', 'International')} cuisine - Rating: 4.5/5\n"
                f"2. Sunset Bistro - {args.get('cuisine', 'Local')} cuisine - Rating: 4.3/5\n"
                "3. Ocean View Grill - Seafood - Rating: 4.6/5"
            ),

            "convert_currency": lambda args: (
                f"{args.get('amount', 0)} {args.get('from_currency', 'USD')} equals "
                f"{args.get('amount', 0) * 0.92:.2f} {args.get('to_currency', 'EUR')} at current exchange rate "
                f"(1 {args.get('from_currency', 'USD')} = 0.92 {args.get('to_currency', 'EUR')})"
            ),

            "get_news": lambda args: (
                f"Latest news on '{args.get('topic')}':\n"
                f"1. Breaking: Major developments in {args.get('topic')} sector (2 hours ago)\n"
                f"2. Expert analysis: What {args.get('topic')} means for the future (5 hours ago)\n"
                f"3. {args.get('topic')} trends show significant growth (1 day ago)"
            ),

            "set_reminder": lambda args: f"Reminder set: '{args.get('message')}' for {args.get('time')} on {args.get('date', 'today')}",

            "translate_text": lambda args: f"Translation to {args.get('target_language')}: [Translated version of '{args.get('text')}']",

            "search_hotels": lambda args: (
                f"Found hotels in {args.get('location')} for {args.get('check_in')} to {args.get('check_out')}:\n"
                "1. Grand Plaza Hotel - $180/night - 4.4 stars\n"
                "2. City Center Inn - $120/night - 4.1 stars\n"
                "3. Luxury Suites - $250/night - 4.7 stars"
            )
        }

        handler = tool_responses.get(name, lambda args: f"Executed {name} with parameters {args}")
        try:
            return handler(arguments)
        except Exception:
            return f"Tool execution completed for {name}"