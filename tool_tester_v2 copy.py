#!/usr/bin/env python3
"""
LLM Tool Calling Test Suite V3.2

Key additions vs V3.1:
- Capability probe to detect what the server actually supports:
  * tools + object tool_choice   (best)
  * tools + "required"           (common on local servers)
  * legacy functions + function_call
- Hard-enforcement: if the user says "Use <tool>" and the model still doesn't
  emit a tool call, we EMULATE the call (and mark llm_initiated=False), so your
  harness still verifies parameters and counts calls.
- Report now shows LLM-initiated vs Emulated counts.

This makes the harness resilient to tool/function support differences across
OpenAI-compatible servers (e.g., llama.cpp style, local gateways, etc.).
"""

import argparse
import json
import sys
import time
import os
import io
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import re
import requests

import cohere
from tool_definitions import ToolDefinitions

# Fix Windows console encoding issues
if sys.platform == 'win32':
    # Set console to UTF-8
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')


# ------------------------ Data classes ------------------------

@dataclass
class ToolCallResult:
    """Store results for a single tool call"""
    tool_name: str
    expected: bool
    parameters_correct: bool              # structural (schema-level) correctness
    execution_successful: bool
    semantic_match: bool = True           # values match what the user asked for
    actual_args: Dict[str, Any] = field(default_factory=dict)
    expected_args: Dict[str, Any] = field(default_factory=dict)
    llm_initiated: bool = True            # False if harness emulated the call
    error: Optional[str] = None


@dataclass
class ExpectedCall:
    """Represents an expected tool call extracted from the user prompt"""
    tool_name: str
    expected_args: Dict[str, Any] = field(default_factory=dict)
    source_text: str = ""


@dataclass
class TestResult:
    """Store results for a single test scenario"""
    scenario_name: str
    description: str
    conversation_turns: int
    tool_calls_made: List[str]
    expected_tool_types: List[str]
    success: bool
    tool_call_details: List[ToolCallResult] = field(default_factory=list)
    expected_tool_call_count: int = 0
    error: Optional[str] = None
    execution_time: float = 0.0
    conversation_log: List[Dict] = field(default_factory=list)


@dataclass
class TestSuite:
    """Collection of test results"""
    name: str
    results: List[TestResult] = field(default_factory=list)

    @property
    def success_rate(self) -> float:
        if not self.results:
            return 0.0
        successful = sum(1 for r in self.results if r.success)
        return (successful / len(self.results)) * 100

    @property
    def total_tool_calls(self) -> int:
        return sum(len(r.tool_calls_made) for r in self.results)


@dataclass
class APICapabilities:
    """What the server supports"""
    supports_tools: bool = False
    supports_tool_choice_object: bool = False
    supports_tool_choice_required: bool = False
    supports_functions: bool = False


# ------------------------ Scenarios ------------------------

class TestScenarios:
    """Natural conversation scenarios that require tool use"""

    @staticmethod
    def get_scenarios() -> List[Dict[str, Any]]:
        """Load scenarios from JSON file or return empty list if file doesn't exist"""
        scenarios_file = "test_scenarios.json"
        
        # Try to find the scenarios file
        if os.path.exists(scenarios_file):
            file_path = scenarios_file
        elif os.path.exists(os.path.join(os.path.dirname(__file__), scenarios_file)):
            file_path = os.path.join(os.path.dirname(__file__), scenarios_file)
        else:
            print(f"Warning: {scenarios_file} not found. Using empty scenario list.")
            return []
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return data.get('scenarios', [])
        except Exception as e:
            print(f"Error loading scenarios from {file_path}: {e}")
            return []

    @staticmethod
    def get_scenario_by_complexity(min_tools: int, max_tools: int) -> List[Dict[str, Any]]:
        """Get scenarios that require a specific number of tool calls"""
        all_scenarios = TestScenarios.get_scenarios()
        filtered = []
        for scenario in all_scenarios:
            expected_count = len(scenario["expected_tools"]) + len(scenario.get("follow_ups", []))
            if min_tools <= expected_count <= max_tools:
                filtered.append(scenario)
        return filtered


# ------------------------ Core tester ------------------------

class LLMToolTester:
    """Main test runner for natural tool calling"""

    def __init__(self, api_base: str = None, api_key: str = "FEBYH8H2ogbtTRaBmChNCaz3aMnmE3EtBkNQWYv5", model: str = "command-a-03-2025", debug: bool = False, provider: str = None, temperature: float = None, reasoning_effort: str = None):
        self.api_base = api_base
        self.api_key = api_key
        self.model = model
        self.provider = provider
        self.temperature = temperature if temperature is not None else 0.1
        self.reasoning_effort = reasoning_effort
        self.is_openrouter = False  # Not using OpenRouter for Cohere

        # Initialize Cohere client
        self.client = cohere.ClientV2(api_key=api_key)

        # Get only 5 tools for testing
        all_tools = ToolDefinitions.get_all_tools()
        self.tools = all_tools[:5]  # Limit to first 5 tools
        self.debug = debug
        self.capabilities: Optional[APICapabilities] = None

    # ---------- Capability probe ----------

    def _tools_to_functions(self) -> List[Dict[str, Any]]:
        """Convert modern tools list to legacy functions schema for fallback."""
        funcs = []
        for t in self.tools:
            if t.get("type") == "function":
                f = t["function"]
                funcs.append({
                    "name": f["name"],
                    "description": f.get("description", ""),
                    "parameters": f.get("parameters", {"type": "object", "properties": {}})
                })
        return funcs

    def _probe_capabilities(self) -> APICapabilities:
        caps = APICapabilities()
        probe_msgs = [
            {"role": "system", "content": "You are a tool-calling probe."},
            {"role": "user", "content": "Use the calculate tool to compute 2+2."}
        ]

        # Try tools + object tool_choice
        try:
            self.client.chat.completions.create(
                model=self.model,
                messages=probe_msgs,
                tools=self.tools,
                tool_choice={"type": "function", "function": {"name": "calculate"}},
                temperature=self.temperature,
                max_tokens=1,
                timeout=10.0
            )
            caps.supports_tools = True
            caps.supports_tool_choice_object = True
            if self.debug:
                print("  [Probe] tools + object tool_choice: OK")
            return caps
        except Exception as e:
            if self.debug:
                print(f"  [Probe] tools + object tool_choice: FAIL ({e})")

        # Try tools + "required"
        try:
            self.client.chat.completions.create(
                model=self.model,
                messages=probe_msgs,
                tools=self.tools,
                tool_choice="required",
                temperature=self.temperature,
                max_tokens=1,
                timeout=10.0
            )
            caps.supports_tools = True
            caps.supports_tool_choice_required = True
            if self.debug:
                print("  [Probe] tools + 'required': OK")
        except Exception as e:
            if self.debug:
                print(f"  [Probe] tools + 'required': FAIL ({e})")

        # Try legacy functions + function_call
        try:
            self.client.chat.completions.create(
                model=self.model,
                messages=probe_msgs,
                functions=self._tools_to_functions(),
                function_call={"name": "calculate"},
                temperature=self.temperature,
                max_tokens=1,
                timeout=10.0
            )
            caps.supports_functions = True
            if self.debug:
                print("  [Probe] legacy functions + function_call: OK")
        except Exception as e:
            if self.debug:
                print(f"  [Probe] legacy functions + function_call: FAIL ({e})")

        return caps

    # ---------- Expected-call extraction helpers ----------

    def _extract_location(self, text: str) -> Optional[str]:
        m = re.search(r"\b(?:in|for)\s+([A-Z][A-Za-z]+(?:[ -][A-Z][A-Za-z]+)*)", text)
        if not m:
            return None
        loc = m.group(1)
        # Filter out month/time words accidentally captured (e.g., "in July")
        blacklist = {
            "January","February","March","April","May","June","July","August","September","October","November","December",
            "Today","Tomorrow","Tonight","Morning","Afternoon","Evening","Weekend","Week","Noon","Midnight"
        }
        if loc.capitalize() in blacklist:
            return None
        return loc

    def _extract_flight_triplet(self, text: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        from_city = to_city = date = None
        m = re.search(
            r"from\s+([A-Z][\w\s-]+?)\s+to\s+([A-Z][\w\s-]+?)(?:\s+on\s+([A-Za-z]+\s+\d{1,2}\w{0,2}|\d{4}-\d{2}-\d{2}))?",
            text, re.IGNORECASE
        )
        if m:
            from_city, to_city, date = m.group(1), m.group(2), m.group(3)
        return from_city, to_city, date

    def _extract_stock_symbols(self, text: str) -> List[str]:
        return re.findall(r"\(([A-Z]{1,6})\)", text)

    def _extract_currency_triplet(self, text: str) -> Tuple[Optional[float], Optional[str], Optional[str]]:
        amt = None
        m_amt = re.search(r"\$?\s?(\d[\d,]*(?:\.\d+)?)", text)
        if m_amt:
            amt = float(m_amt.group(1).replace(",", ""))

        def norm(code: str) -> str:
            mapping = {
                # Core
                "usd": "USD", "eur": "EUR", "gbp": "GBP", "jpy": "JPY", "yen": "JPY",
                "pounds": "GBP", "pound": "GBP", "euros": "EUR", "euro": "EUR", "dollars": "USD", "dollar": "USD",
                # Extended/common in scenarios
                "aud": "AUD", "australian dollars": "AUD", "australian dollar": "AUD",
                "cad": "CAD", "canadian dollars": "CAD", "canadian dollar": "CAD",
                "sgd": "SGD", "singapore dollars": "SGD", "singapore dollar": "SGD",
                "aed": "AED", "uae dirhams": "AED", "uae dirham": "AED", "dirhams": "AED", "dirham": "AED",
                "cny": "CNY", "rmb": "CNY", "renminbi": "CNY", "chinese yuan": "CNY", "yuan": "CNY",
                "chf": "CHF", "swiss francs": "CHF", "swiss franc": "CHF",
                "sek": "SEK", "swedish krona": "SEK", "krona": "SEK"
            }
            return mapping.get(code.lower(), code.upper())

        m_from = re.search(r"\bfrom\s+([A-Za-z]{3,})\b", text)
        m_to = re.search(r"\bto\s+([A-Za-z]{3,}|[A-Za-z]+(?:\s+pounds|dollars|euros|yen))\b", text)
        from_ccy = norm(m_from.group(1)) if m_from else None
        to_ccy_raw = m_to.group(1) if m_to else None
        to_ccy = norm(to_ccy_raw) if to_ccy_raw else None

        if amt and not from_ccy:
            if "$" in text:
                from_ccy = "USD"
        return amt, from_ccy, to_ccy

    def _extract_news_topic(self, text: str) -> Optional[str]:
        m = re.search(r"news\s+(?:about|on|regarding|around)\s+(.+)$", text, re.IGNORECASE)
        if not m:
            return None
        topic = m.group(1).strip()
        # Strip trailing punctuation
        topic = topic.rstrip(".?! ")
        return topic if topic else None

    def _extract_translate(self, text: str) -> Dict[str, Any]:
        # Prefer quoted text pattern
        m = re.search(r"translate\s+[\"'](.+?)[\"']\s+to\s+([A-Za-z ]+)", text, re.IGNORECASE)
        if m:
            return {"text": m.group(1), "target_language": m.group(2).strip()}
        # Fallback: only target language
        m2 = re.search(r"translate(?:\s+it|\s+this|\s+the message)?\s+to\s+([A-Za-z ]+)", text, re.IGNORECASE)
        if m2:
            return {"target_language": m2.group(1).strip()}
        return {}

    def _extract_calculate_expression(self, text: str) -> Optional[str]:
        m = re.search(r"(?:^|\b)calculate\b[:\s]*(.+)$", text, re.IGNORECASE)
        if m:
            return m.group(1).strip()
        return None

    def _stock_name_to_ticker(self, text: str) -> List[str]:
        mapping = {
            "apple": "AAPL",
            "microsoft": "MSFT",
            "google": "GOOGL",
            "alphabet": "GOOGL",
            "amazon": "AMZN",
            "tesla": "TSLA"
        }
        found = []
        low = text.lower()
        for name, ticker in mapping.items():
            if re.search(rf"\b{name}\b", low):
                found.append(ticker)
        # Also capture standalone uppercase tickers not in parentheses (2-6 letters)
        for tok in re.findall(r"\b[A-Z]{2,6}\b", text):
            if tok not in found:
                found.append(tok)
        return found

    def _extract_args_for_tool(self, name: str, text: str, context: Dict[str, Any]) -> List[ExpectedCall]:
        calls: List[ExpectedCall] = []
        expected_args: Dict[str, Any] = {}

        if name == "get_weather":
            loc = self._extract_location(text) or context.get("last_location")
            if loc:
                expected_args["location"] = loc
                context["last_location"] = loc
            calls.append(ExpectedCall(name, expected_args, text))
            return calls

        if name == "search_flights":
            f, t, d = self._extract_flight_triplet(text)
            if f: expected_args["from_city"] = f
            if t: expected_args["to_city"] = t
            if d: expected_args["date"] = d
            calls.append(ExpectedCall(name, expected_args, text))
            return calls

        if name == "get_stock_price":
            symbols = self._extract_stock_symbols(text)
            if not symbols:
                symbols = self._stock_name_to_ticker(text)
            if symbols:
                for s in symbols:
                    calls.append(ExpectedCall(name, {"symbol": s}, text))
                return calls
            calls.append(ExpectedCall(name, {}, text))
            return calls

        if name == "convert_currency":
            amt, fr, to = self._extract_currency_triplet(text)
            if amt is not None: expected_args["amount"] = amt
            if fr: expected_args["from_currency"] = fr
            if to: expected_args["to_currency"] = to
            calls.append(ExpectedCall(name, expected_args, text))
            return calls

        if name in ("search_restaurants", "search_hotels"):
            loc = self._extract_location(text) or context.get("last_location")
            if loc:
                expected_args["location"] = loc
                context["last_location"] = loc
            calls.append(ExpectedCall(name, expected_args, text))
            return calls

        if name == "translate_text":
            args = self._extract_translate(text)
            if args:
                calls.append(ExpectedCall(name, args, text))
                return calls
            calls.append(ExpectedCall(name, {}, text))
            return calls

        if name == "get_news":
            topic = self._extract_news_topic(text)
            if topic:
                calls.append(ExpectedCall(name, {"topic": topic}, text))
                return calls
            calls.append(ExpectedCall(name, {}, text))
            return calls

        if name == "set_reminder":
            m_time = re.search(r"\b(?:at|@)\s+(\d{1,2}:\d{2}|\d{1,2}\s?(?:am|pm)|noon|midnight)\b", text, re.IGNORECASE)
            if m_time:
                expected_args["time"] = m_time.group(1)
            m_msg = re.search(r"set a reminder(?:\s+to)?\s+(.+?)(?:\s+at|\s+on|$)", text, re.IGNORECASE)
            if m_msg:
                expected_args["message"] = m_msg.group(1).strip()
            calls.append(ExpectedCall(name, expected_args, text))
            return calls

        if name == "calculate":
            expr = self._extract_calculate_expression(text)
            if expr:
                calls.append(ExpectedCall(name, {"expression": expr}, text))
                return calls
            # As fallback, use the whole text after 'calculate' if present elsewhere
            calls.append(ExpectedCall(name, {}, text))
            return calls

        # Default
        calls.append(ExpectedCall(name, {}, text))
        return calls

    def _build_expected_calls_from_text(self, text: str, context: Dict[str, Any]) -> List[ExpectedCall]:
        calls: List[ExpectedCall] = []
        low = text.lower()

        # 1) Explicit: capture ALL "use <tool>" mentions in order of appearance
        occurrences: List[Tuple[int, str]] = []
        for t in self.tools:
            name = t["function"]["name"]
            for m in re.finditer(rf"use\s+(?:the\s+)?{re.escape(name)}\b", low):
                occurrences.append((m.start(), name))
        occurrences.sort(key=lambda x: x[0])

        if occurrences:
            # Build segments between occurrences to improve per-tool arg extraction
            for idx, (pos, name) in enumerate(occurrences):
                seg_start = pos
                seg_end = occurrences[idx + 1][0] if idx + 1 < len(occurrences) else len(text)
                seg_text = text[seg_start:seg_end]
                calls.extend(self._extract_args_for_tool(name, seg_text, context))
            return calls

        # 2) Implicit fallbacks (broadened)
        if "weather" in low:
            loc = self._extract_location(text) or context.get("last_location")
            if loc:
                context["last_location"] = loc
            calls.append(ExpectedCall("get_weather", {"location": loc} if loc else {}, text))

        if ("hotel" in low or "hotels" in low):
            loc = self._extract_location(text) or context.get("last_location")
            if loc:
                context["last_location"] = loc
            calls.append(ExpectedCall("search_hotels", {"location": loc} if loc else {}, text))

        if "restaurant" in low:
            loc = self._extract_location(text) or context.get("last_location")
            if loc:
                context["last_location"] = loc
            calls.append(ExpectedCall("search_restaurants", {"location": loc} if loc else {}, text))

        if "flight" in low:
            f, t, d = self._extract_flight_triplet(text)
            ec_args = {}
            if f: ec_args["from_city"] = f
            if t: ec_args["to_city"] = t
            if d: ec_args["date"] = d
            calls.append(ExpectedCall("search_flights", ec_args, text))

        if "calculate" in low:
            expr = self._extract_calculate_expression(text)
            calls.append(ExpectedCall("calculate", {"expression": expr} if expr else {}, text))

        if "news" in low:
            topic = self._extract_news_topic(text)
            calls.append(ExpectedCall("get_news", {"topic": topic} if topic else {}, text))

        if "convert" in low:
            amt, fr, to = self._extract_currency_triplet(text)
            args = {}
            if amt is not None: args["amount"] = amt
            if fr: args["from_currency"] = fr
            if to: args["to_currency"] = to
            calls.append(ExpectedCall("convert_currency", args, text))

        if "translate" in low:
            args = self._extract_translate(text)
            calls.append(ExpectedCall("translate_text", args, text))

        if "stock" in low:
            symbols = self._extract_stock_symbols(text)
            if not symbols:
                symbols = self._stock_name_to_ticker(text)
            if symbols:
                for s in symbols:
                    calls.append(ExpectedCall("get_stock_price", {"symbol": s}, text))
            else:
                calls.append(ExpectedCall("get_stock_price", {}, text))

        return calls

    def _build_expected_queue_for_scenario(self, scenario: Dict[str, Any]) -> List[ExpectedCall]:
        ctx: Dict[str, Any] = {}
        queue: List[ExpectedCall] = []
        queue.extend(self._build_expected_calls_from_text(scenario["initial_prompt"], ctx))
        for fu in scenario.get("follow_ups", []):
            queue.extend(self._build_expected_calls_from_text(fu, ctx))
        return queue

    def _match_expected_call(self, tool_name: str, args: Dict[str, Any], expected_queue: List[ExpectedCall]) -> Tuple[bool, bool, Dict[str, Any]]:
        for i, exp in enumerate(expected_queue):
            if exp.tool_name == tool_name:
                exp_used = expected_queue[i]
                sem_ok = True
                for k, v in exp_used.expected_args.items():
                    if v in (None, ""):
                        continue
                    a = args.get(k)
                    if a is None:
                        sem_ok = False
                        break
                    if isinstance(v, str) and v.strip():
                        if v.lower() not in str(a).lower():
                            sem_ok = False
                            break
                    elif isinstance(v, (int, float)):
                        try:
                            sem_ok = abs(float(a) - float(v)) < 1e-6
                        except Exception:
                            sem_ok = False
                        if not sem_ok:
                            break
                # If no keys to check, treat as semantic miss (unless tool truly needs none)
                if not exp_used.expected_args:
                    sem_ok = False
                # Special-case: for calculate, accept if expression contains a number
                if tool_name == "calculate":
                    expr = args.get("expression")
                    if isinstance(expr, str) and re.search(r"\d", expr):
                        sem_ok = True
                if sem_ok:
                    expected_queue.pop(i)
                return True, sem_ok, exp_used.expected_args
        return False, False, {}

    # ---------- Utilities ----------

    def _detect_forced_tool_name(self, messages: List[Dict[str, Any]]) -> Optional[str]:
        last_user = None
        for m in reversed(messages):
            if m.get("role") == "user":
                last_user = m.get("content", "")
                break
        if not last_user:
            return None
        low = last_user.lower()
        for t in self.tools:
            name = t["function"]["name"]
            if f"use {name.lower()}" in low or f"use the {name.lower()}" in low:
                return name
        return None

    def _message_to_dict(self, message) -> Dict:
        """Normalize assistant message for re-sending (both tool_calls and function_call)."""
        try:
            return message.model_dump()
        except AttributeError:
            msg = {"role": "assistant"}
            msg["content"] = getattr(message, "content", "") or ""

            # tool_calls normalization
            tc_list = getattr(message, "tool_calls", None)
            if tc_list:
                norm = []
                for tc in tc_list:
                    try:
                        norm.append({
                            "id": getattr(tc, "id", None),
                            "type": getattr(tc, "type", "function"),
                            "function": {
                                "name": getattr(getattr(tc, "function", None), "name", None),
                                "arguments": getattr(getattr(tc, "function", None), "arguments", "{}")
                            }
                        })
                    except Exception:
                        pass
                if norm:
                    msg["tool_calls"] = norm

            # function_call normalization
            fc = getattr(message, "function_call", None)
            if fc:
                try:
                    msg["function_call"] = {
                        "name": getattr(fc, "name", None),
                        "arguments": getattr(fc, "arguments", "{}")
                    }
                except Exception:
                    pass

            return msg

    def _ensure_capabilities(self):
        if self.capabilities is None:
            print("  Probing server capabilities...")
            self.capabilities = self._probe_capabilities()
            if self.debug:
                print(f"  Capabilities: {self.capabilities}")

    def _chat_request_openrouter(self, messages: List[Dict[str, Any]], forced_name: Optional[str]):
        """
        Direct request to OpenRouter API bypassing OpenAI client to properly handle provider routing.
        Returns (response_obj, mode_str) matching the format of regular _chat_request.
        """
        print(f"  [OpenRouter] Sending request to OpenRouter...")

        # Build URL
        if '/v1' in self.api_base:
            url = f"{self.api_base}/chat/completions"
        else:
            url = f"{self.api_base}/v1/chat/completions"
        
        # Build request body with all parameters
        body = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": 1000,
            "stream": False
        }
        
        # Add provider routing - using "require" to force specific provider
        if self.provider:
            # Use "require" to force the provider (will fail if unavailable)
            # Alternative: use "order" for preference with fallback
            body["provider"] = {"order": [self.provider], "allow_fallbacks": False}
            if self.debug:
                print(f"  [OpenRouter] Requiring provider: {self.provider} (forced, no fallback)")
        
        # Add reasoning if specified
        if self.reasoning_effort:
            body["reasoning"] = {
                "effort": self.reasoning_effort,
                "max_tokens": 1000,
                "exclude": False,
                "enabled": True
            }
        
        # Add tools support
        if self.tools:
            body["tools"] = self.tools
            if forced_name:
                body["tool_choice"] = {"type": "function", "function": {"name": forced_name}}
            else:
                body["tool_choice"] = "auto"
        
        # Prepare headers
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://tool-tester",  # Optional but recommended for OpenRouter
            "X-Title": "Tool Tester"  # Optional but recommended for OpenRouter
        }
        
        print(f"  [OpenRouter Request] URL: {url}")
        print(f"  [OpenRouter Request] Provider in body: {body.get('provider')}")
        print(f"  [OpenRouter Request] Body: {body}")
        
        # Retry logic for rate limiting and provider downtime
        max_retries = 3
        max_404_retries = 5  # More retries for provider downtime
        retry_count = 0
        consecutive_404s = 0
        base_wait_time = 5  # Start with 5 seconds
        base_404_wait_time = 15  # Longer waits for 404s
        
        while retry_count <= max_retries:
            try:
                # Make the request
                response = requests.post(url, json=body, headers=headers, timeout=30.0)
                
                # Check for rate limiting (429)
                if response.status_code == 429:
                    retry_count += 1
                    if retry_count > max_retries:
                        error_detail = ""
                        try:
                            error_json = response.json()
                            error_detail = f" - {error_json}"
                        except:
                            error_detail = f" - {response.text}"
                        raise ValueError(f"OpenRouter API error {response.status_code} (rate limited after {max_retries} retries){error_detail}")
                    
                    # Calculate wait time with exponential backoff
                    wait_time = base_wait_time * (2 ** (retry_count - 1))
                    
                    # Check for Retry-After header
                    retry_after = response.headers.get('Retry-After')
                    if retry_after:
                        try:
                            wait_time = int(retry_after)
                            print(f"  [OpenRouter] Rate limited. Waiting {wait_time}s as requested by server...")
                        except:
                            print(f"  [OpenRouter] Rate limited. Waiting {wait_time}s (exponential backoff)...")
                    else:
                        print(f"  [OpenRouter] Rate limited. Waiting {wait_time}s (exponential backoff)...")
                    
                    time.sleep(wait_time)
                    continue
                
                # Check for provider downtime (404 - No endpoints found)
                if response.status_code == 404:
                    consecutive_404s += 1
                    retry_count += 1
                    
                    # Check if we've hit too many 404s - abort the entire test
                    if consecutive_404s > max_404_retries:
                        print(f"  [OpenRouter] Provider appears to be down after {max_404_retries} attempts. Aborting test.")
                        # Set a special flag to indicate provider failure
                        raise ValueError("PROVIDER_DOWN")
                    
                    if retry_count > max_retries:
                        error_detail = ""
                        try:
                            error_json = response.json()
                            error_detail = f" - {error_json}"
                        except:
                            error_detail = f" - {response.text}"
                        raise ValueError(f"OpenRouter API error {response.status_code} (provider down after {max_retries} retries){error_detail}")
                    
                    # Longer wait for provider downtime
                    wait_time = min(60, base_404_wait_time * consecutive_404s)  # Cap at 1 minute
                    print(f"  [OpenRouter] Provider down (attempt {consecutive_404s}/{max_404_retries}). Waiting {wait_time}s...")
                    
                    time.sleep(wait_time)
                    continue
                else:
                    # Reset 404 counter on success or other errors
                    consecutive_404s = 0
                
                # Check for other errors
                if response.status_code != 200:
                    error_detail = ""
                    try:
                        error_json = response.json()
                        error_detail = f" - {error_json}"
                    except:
                        error_detail = f" - {response.text}"
                    raise ValueError(f"OpenRouter API error {response.status_code}{error_detail}")
                
                # Success - break out of retry loop
                break
                
            except requests.exceptions.Timeout:
                retry_count += 1
                if retry_count > max_retries:
                    raise ValueError(f"OpenRouter request timed out after {max_retries} retries")
                wait_time = base_wait_time * (2 ** (retry_count - 1))
                print(f"  [OpenRouter] Request timeout. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            except requests.exceptions.ConnectionError as e:
                retry_count += 1
                if retry_count > max_retries:
                    raise ValueError(f"OpenRouter connection error after {max_retries} retries: {str(e)}")
                wait_time = base_wait_time * (2 ** (retry_count - 1))
                print(f"  [OpenRouter] Connection error. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            
        response.raise_for_status()
        
        # Parse response
        response_data = response.json()
        
        # Convert to OpenAI-like response object structure
        # We need to create a mock object that matches what the OpenAI client returns
        class MockMessage:
            def __init__(self, data):
                self.content = data.get("content", "")
                self.role = data.get("role", "assistant")
                self.tool_calls = None
                self.function_call = None
                
                # Handle tool calls if present
                if "tool_calls" in data:
                    self.tool_calls = []
                    for tc in data["tool_calls"]:
                        tool_call = type('ToolCall', (), {
                            'id': tc.get('id'),
                            'type': tc.get('type', 'function'),
                            'function': type('Function', (), {
                                'name': tc.get('function', {}).get('name'),
                                'arguments': tc.get('function', {}).get('arguments', '{}')
                            })()
                        })()
                        self.tool_calls.append(tool_call)
                
                # Handle legacy function_call if present
                if "function_call" in data:
                    fc = data["function_call"]
                    self.function_call = type('FunctionCall', (), {
                        'name': fc.get('name'),
                        'arguments': fc.get('arguments', '{}')
                    })()
        
        class MockChoice:
            def __init__(self, choice_data):
                msg_data = choice_data.get("message", {})
                self.message = MockMessage(msg_data)
        
        class MockResponse:
            def __init__(self, response_data):
                self.choices = [MockChoice(c) for c in response_data.get("choices", [])]
        
        mock_response = MockResponse(response_data)
        
        # Check if provider was actually used (OpenRouter sometimes includes this in response)
        if self.debug and "provider" in response_data:
            print(f"  [OpenRouter Response] Provider used: {response_data['provider']}")
        
        return mock_response, "openrouter_direct"

    def _chat_request(self, messages: List[Dict[str, Any]], forced_name: Optional[str]):
        """
        Performs a chat request using the best-supported mode based on capability probe.
        Returns (response, mode_str) where mode_str ∈ {"tools_object","tools_required","functions","none","openrouter_direct"}
        """
        # Use direct OpenRouter API if this is an OpenRouter request (to properly handle provider and other params)
        if self.is_openrouter:
            return self._chat_request_openrouter(messages, forced_name)
        
        self._ensure_capabilities()
        caps = self.capabilities

        # Prefer tools + object
        if caps.supports_tools and caps.supports_tool_choice_object:
            tool_choice = {"type": "function", "function": {"name": forced_name}} if forced_name else "auto"
            if self.debug:
                print(f"  [Request] mode=tools_object, forced={forced_name}")
            
            kwargs = {
                "model": self.model,
                "messages": messages,
                "tools": self.tools,
                "tool_choice": tool_choice,
                "temperature": self.temperature,
                "max_tokens": 1000,
                "timeout": 30.0
            }
            
            resp = self.client.chat.completions.create(**kwargs)
            return resp, "tools_object"

        # Next: tools + "required"
        if caps.supports_tools and caps.supports_tool_choice_required:
            # We can't force the exact tool name, but "required" nudges the model to call a tool.
            if self.debug:
                print(f"  [Request] mode=tools_required, forced={forced_name} (hinted via text)")
            
            kwargs = {
                "model": self.model,
                "messages": messages,
                "tools": self.tools,
                "tool_choice": "required" if forced_name else "auto",
                "temperature": self.temperature,
                "max_tokens": 1000,
                "timeout": 30.0
            }
            
            resp = self.client.chat.completions.create(**kwargs)
            return resp, "tools_required"

        # Legacy functions
        if caps.supports_functions:
            kwargs = {
                "model": self.model,
                "messages": messages,
                "functions": self._tools_to_functions(),
                "temperature": self.temperature,
                "max_tokens": 1000,
                "timeout": 30.0
            }
            if forced_name:
                kwargs["function_call"] = {"name": forced_name}
            
            if self.debug:
                print(f"  [Request] mode=functions, forced={forced_name}")
            resp = self.client.chat.completions.create(**kwargs)
            return resp, "functions"

        # No tool support detected
        if self.debug:
            print("  [Request] mode=none (no tool support detected)")
        
        kwargs = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": 1000,
            "timeout": 30.0
        }
        
        resp = self.client.chat.completions.create(**kwargs)
        return resp, "none"

    # ---------- Scenario runner ----------

    def run_scenario(self, scenario: Dict[str, Any]) -> TestResult:
        start_time = time.time()
        conversation_log = []
        tool_calls_made: List[str] = []
        tool_call_details: List[ToolCallResult] = []

        expected_queue = self._build_expected_queue_for_scenario(scenario)
        total_expected_count = len(expected_queue)

        try:
            messages = [
                {
                    "role": "system",
                    "content": (
                        "You are a helpful assistant. When the user explicitly names a tool to use, "
                        "you MUST call that tool and MUST NOT fabricate results. When a tool is available "
                        "that directly answers the user's request (e.g., weather, flights, hotels, stocks, currency), "
                        "prefer calling the tool over answering from prior knowledge. "
                        "Do not ignore available tools."
                    ),
                },
                {"role": "user", "content": scenario["initial_prompt"]}
            ]
            conversation_log.append({"role": "user", "content": scenario["initial_prompt"]})

            all_prompts = [scenario["initial_prompt"]] + scenario.get("follow_ups", [])
            user_prompt_index = 0
            max_turns = max(6, (len(all_prompts) + total_expected_count) * 2)

            for _ in range(max_turns):
                forced_name = self._detect_forced_tool_name(messages)
                response, mode = self._chat_request(messages, forced_name)

                if not response.choices:
                    raise ValueError("No response choices")

                assistant_message = response.choices[0].message
                messages.append(self._message_to_dict(assistant_message))

                if assistant_message.content:
                    conversation_log.append({"role": "assistant", "content": assistant_message.content})

                did_tool_something = False

                # --- Modern tools path: multiple tool_calls possible ---
                if hasattr(assistant_message, "tool_calls") and assistant_message.tool_calls:
                    did_tool_something = True
                    for tc in assistant_message.tool_calls:
                        tool_name = tc.function.name
                        tool_calls_made.append(tool_name)

                        try:
                            args = json.loads(tc.function.arguments or "{}")
                        except Exception:
                            args = {}

                        params_valid = ToolDefinitions.validate_parameters(tool_name, args)

                        try:
                            result = ToolDefinitions.execute_tool(tool_name, args)
                            exec_ok = True
                            exec_err = None
                        except Exception as e:
                            result = f"Error executing {tool_name}: {str(e)}"
                            exec_ok = False
                            exec_err = str(e)

                        is_expected, sem_ok, exp_args = self._match_expected_call(tool_name, args, expected_queue)

                        tool_call_details.append(ToolCallResult(
                            tool_name=tool_name,
                            expected=is_expected,
                            parameters_correct=params_valid,
                            execution_successful=exec_ok,
                            semantic_match=sem_ok if is_expected else False,
                            actual_args=args,
                            expected_args=exp_args,
                            llm_initiated=True,
                            error=exec_err
                        ))

                        # Return tool result (modern format)
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tc.id,
                            "name": tool_name,
                            "content": result
                        })
                        conversation_log.append({"role": "tool", "name": tool_name, "content": result if len(result) < 200 else result[:197] + "..."})

                # --- Legacy functions path: single function_call ---
                elif hasattr(assistant_message, "function_call") and assistant_message.function_call:
                    did_tool_something = True
                    fc = assistant_message.function_call
                    tool_name = fc.name
                    tool_calls_made.append(tool_name)

                    try:
                        args = json.loads(fc.arguments or "{}")
                    except Exception:
                        args = {}

                    params_valid = ToolDefinitions.validate_parameters(tool_name, args)

                    try:
                        result = ToolDefinitions.execute_tool(tool_name, args)
                        exec_ok = True
                        exec_err = None
                    except Exception as e:
                        result = f"Error executing {tool_name}: {str(e)}"
                        exec_ok = False
                        exec_err = str(e)

                    is_expected, sem_ok, exp_args = self._match_expected_call(tool_name, args, expected_queue)

                    tool_call_details.append(ToolCallResult(
                        tool_name=tool_name,
                        expected=is_expected,
                        parameters_correct=params_valid,
                        execution_successful=exec_ok,
                        semantic_match=sem_ok if is_expected else False,
                        actual_args=args,
                        expected_args=exp_args,
                        llm_initiated=True,
                        error=exec_err
                    ))

                    # Return tool result (legacy format uses role=function)
                    messages.append({
                        "role": "function",
                        "name": tool_name,
                        "content": result
                    })
                    conversation_log.append({"role": "function", "name": tool_name, "content": result if len(result) < 200 else result[:197] + "..."})

                # No emulation: if forced tool wasn't called, we proceed without executing it

                # If a tool was called (or emulated), let the loop iterate again to allow follow-ups.
                if did_tool_something:
                    continue

                # No tool call this turn: push next follow-up if available
                if user_prompt_index < len(scenario.get("follow_ups", [])):
                    next_prompt = scenario["follow_ups"][user_prompt_index]
                    messages.append({"role": "user", "content": next_prompt})
                    conversation_log.append({"role": "user", "content": next_prompt})
                    user_prompt_index += 1
                    continue

                # No more prompts, break
                break

            # Evaluate success
            matched_calls_llm = sum(1 for tc in tool_call_details if tc.llm_initiated and tc.expected and tc.parameters_correct and tc.semantic_match)
            structural_ok_llm = sum(1 for tc in tool_call_details if tc.llm_initiated and tc.parameters_correct)

            success = (
                (total_expected_count == 0 or matched_calls_llm >= max(1, int(0.7 * total_expected_count)))
                and (structural_ok_llm >= int(0.6 * max(1, sum(1 for tc in tool_call_details if tc.llm_initiated))))
            )

            execution_time = time.time() - start_time

            return TestResult(
                scenario_name=scenario["name"],
                description=scenario["description"],
                conversation_turns=len([m for m in conversation_log if m["role"] in ["user", "assistant"]]),
                tool_calls_made=tool_calls_made,
                tool_call_details=tool_call_details,
                expected_tool_types=scenario["expected_tools"],
                expected_tool_call_count=total_expected_count,
                success=success,
                execution_time=execution_time,
                conversation_log=conversation_log
            )

        except Exception as e:
            execution_time = time.time() - start_time
            # Check if this is a provider down error
            if str(e) == "PROVIDER_DOWN":
                print(f"    Provider down - skipping remaining tests")
                return TestResult(
                    scenario_name=scenario["name"],
                    description=scenario["description"],
                    conversation_turns=0,
                    tool_calls_made=[],
                    tool_call_details=[],
                    expected_tool_types=scenario["expected_tools"],
                    expected_tool_call_count=total_expected_count,
                    success=False,
                    error="Provider unavailable",
                    execution_time=execution_time,
                    conversation_log=[]
                )
            
            return TestResult(
                scenario_name=scenario["name"],
                description=scenario["description"],
                conversation_turns=len([m for m in conversation_log if m["role"] in ["user", "assistant"]]),
                tool_calls_made=tool_calls_made,
                tool_call_details=tool_call_details,
                expected_tool_types=scenario["expected_tools"],
                expected_tool_call_count=total_expected_count,
                success=False,
                error=str(e),
                execution_time=execution_time,
                conversation_log=conversation_log
            )

    # ------------------------ Suite & Report ------------------------

    def run_test_suite(self, suite_name: str, scenarios: List[Dict[str, Any]]) -> TestSuite:
        suite = TestSuite(name=suite_name)
        provider_down = False
        
        for scenario in scenarios:
            print(f"\n  Testing: {scenario['name']} - {scenario['description']}")
            result = self.run_scenario(scenario)
            suite.results.append(result)

            status = "PASS" if result.success else "FAIL"
            print(f"    {status} Completed: {len(result.tool_calls_made)} tool calls in {result.execution_time:.2f}s")
            if result.error:
                # Handle Unicode encoding issues on Windows
                try:
                    print(f"      Error: {result.error}")
                except UnicodeEncodeError:
                    # Replace problematic characters for console output
                    safe_error = result.error.encode('ascii', 'replace').decode('ascii')
                    print(f"      Error: {safe_error}")
                
            # Check if provider is down - if so, create empty results for remaining scenarios
            if result.error == "Provider unavailable":
                provider_down = True
                print(f"  Provider is down - creating empty results for remaining scenarios")
                break
        
        # If provider is down, create zero results for remaining scenarios
        if provider_down:
            remaining_scenarios = scenarios[len(suite.results):]
            for scenario in remaining_scenarios:
                try:
                    exp_queue = self._build_expected_queue_for_scenario(scenario)
                    expected_count = len(exp_queue)
                except Exception:
                    expected_count = len(scenario.get("expected_tools", [])) + len(scenario.get("follow_ups", []))
                empty_result = TestResult(
                    scenario_name=scenario["name"],
                    description=scenario["description"],
                    conversation_turns=0,
                    tool_calls_made=[],
                    tool_call_details=[],
                    expected_tool_types=scenario["expected_tools"],
                    expected_tool_call_count=expected_count,
                    success=False,
                    error="Provider unavailable",
                    execution_time=0.0,
                    conversation_log=[]
                )
                suite.results.append(empty_result)
        
        return suite

    def generate_report(self, suites: List[TestSuite]) -> str:
        def clamp01(x: float) -> float:
            return 0.0 if x <= 0 else (1.0 if x >= 1 else x)

        report = []
        report.append("\n" + "="*60)
        report.append("LLM NATURAL TOOL CALLING TEST REPORT")
        report.append("="*60)
        report.append(f"Model: {self.model}")
        report.append(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        total_tests = sum(len(suite.results) for suite in suites)
        total_success = sum(sum(1 for r in suite.results if r.success) for suite in suites)

        # Totals
        total_expected = sum(r.expected_tool_call_count for suite in suites for r in suite.results)
        total_attempted_all = sum(len(r.tool_call_details) for suite in suites for r in suite.results)
        total_attempted_llm = sum(
            sum(1 for tc in r.tool_call_details if tc.llm_initiated)
            for suite in suites for r in suite.results
        )
        total_matched_llm = sum(
            sum(
                1 for tc in r.tool_call_details
                if tc.llm_initiated and tc.expected and tc.parameters_correct and tc.semantic_match
            )
            for suite in suites for r in suite.results
        )
        total_params_correct = sum(
            sum(1 for tc in r.tool_call_details if tc.parameters_correct)
            for suite in suites for r in suite.results
        )
        total_execution_success = sum(
            sum(1 for tc in r.tool_call_details if tc.execution_successful)
            for suite in suites for r in suite.results
        )
        total_semantic_match = sum(
            sum(1 for tc in r.tool_call_details if tc.semantic_match)
            for suite in suites for r in suite.results
        )

        total_params_correct_llm = sum(
            sum(1 for tc in r.tool_call_details if tc.llm_initiated and tc.parameters_correct)
            for suite in suites for r in suite.results
        )
        total_execution_success_llm = sum(
            sum(1 for tc in r.tool_call_details if tc.llm_initiated and tc.execution_successful)
            for suite in suites for r in suite.results
        )
        total_semantic_match_llm = sum(
            sum(1 for tc in r.tool_call_details if tc.llm_initiated and tc.semantic_match)
            for suite in suites for r in suite.results
        )

        # Rates (all bounded 0..100 where applicable)
        success_rate = (total_success / total_tests * 100.0) if total_tests > 0 else 0.0

        precision = (total_matched_llm / total_attempted_llm) if total_attempted_llm > 0 else 0.0
        recall = (total_matched_llm / total_expected) if total_expected > 0 else 0.0
        f1 = (2 * precision * recall / (precision + recall)) if (precision + recall) > 0 else 0.0

        param_success_rate = (total_params_correct / total_attempted_all * 100.0) if total_attempted_all > 0 else 0.0
        execution_success_rate = (total_execution_success / total_attempted_all * 100.0) if total_attempted_all > 0 else 0.0
        semantic_match_rate = (total_semantic_match / total_attempted_all * 100.0) if total_attempted_all > 0 else 0.0

        param_success_rate_llm = (total_params_correct_llm / total_attempted_llm * 100.0) if total_attempted_llm > 0 else 0.0
        execution_success_rate_llm = (total_execution_success_llm / total_attempted_llm * 100.0) if total_attempted_llm > 0 else 0.0
        semantic_match_rate_llm = (total_semantic_match_llm / total_attempted_llm * 100.0) if total_attempted_llm > 0 else 0.0

        # Diagnostic factor (can be > 100%, do NOT use in scoring)
        overcall_factor = (total_attempted_all / total_expected) if total_expected > 0 else 0.0  # e.g. x3.76
        overcall_disp = f"x{overcall_factor:.2f}" if total_expected > 0 else "N/A"

        report.append("OVERALL SUMMARY")
        report.append("-"*40)
        report.append(f"Total Scenarios: {total_tests}")
        report.append(f"Successful Scenarios: {total_success}")
        report.append(f"Failed Scenarios: {total_tests - total_success}")
        report.append(f"Scenario Success Rate: {success_rate:.1f}%")
        report.append("")
        report.append("TOOL CALL STATISTICS")
        report.append("-"*25)
        report.append(f"Expected Tool Calls: {total_expected}")
        report.append(f"Attempted Calls (LLM + Emulated): {total_attempted_all}")
        report.append(f"Attempted Calls (LLM only): {total_attempted_llm}")
        report.append(f"Matched Expected (LLM only): {total_matched_llm}")
        report.append(f"Tool Precision (LLM only): {clamp01(precision)*100:.1f}%")
        report.append(f"Tool Recall (LLM only): {clamp01(recall)*100:.1f}%")
        report.append(f"Tool F1 (LLM only): {clamp01(f1)*100:.1f}%")
        report.append(f"Over/Under-call factor (diagnostic): {overcall_disp}")
        
        # Add emulation rate
        emulation_rate = ((total_attempted_all - total_attempted_llm) / max(1, total_attempted_all) * 100.0) if total_attempted_all > 0 else 0.0
        report.append(f"Emulation rate: {emulation_rate:.1f}%")
        report.append(f"Parameter Accuracy (structural): {param_success_rate:.1f}%")
        report.append(f"Parameter Accuracy (LLM only): {param_success_rate_llm:.1f}%")
        report.append(f"Parameter Accuracy (semantic): {semantic_match_rate:.1f}%")
        report.append(f"Parameter Accuracy (semantic, LLM only): {semantic_match_rate_llm:.1f}%")
        report.append(f"Execution Success Rate: {execution_success_rate:.1f}%")
        report.append(f"Execution Success Rate (LLM only): {execution_success_rate_llm:.1f}%")
        report.append("")

        # Per-suite block
        for suite in suites:
            report.append(f"\n{suite.name.upper()}")
            report.append("-"*40)
            report.append(f"Scenarios: {len(suite.results)}")
            report.append(f"Success Rate: {suite.success_rate:.1f}%")
            report.append(f"Total Tool Calls: {suite.total_tool_calls}")

            report.append("\nScenario Details:")
            for result in suite.results:
                status = "PASS" if result.success else "FAIL"
                attempted_all = len(result.tool_call_details)
                attempted_llm = sum(1 for tc in result.tool_call_details if tc.llm_initiated)
                matched_llm = sum(1 for tc in result.tool_call_details if tc.llm_initiated and tc.expected)

                scn_prec = (matched_llm / attempted_llm) if attempted_llm > 0 else 0.0
                scn_recall = (matched_llm / result.expected_tool_call_count) if result.expected_tool_call_count > 0 else 0.0
                scn_f1 = (2 * scn_prec * scn_recall / (scn_prec + scn_recall)) if (scn_prec + scn_recall) > 0 else 0.0
                scn_over = (attempted_all / result.expected_tool_call_count) if result.expected_tool_call_count > 0 else 0.0
                scn_over_disp = f"x{scn_over:.2f}" if result.expected_tool_call_count > 0 else "N/A"

                report.append(f"\n  [{status}] {result.scenario_name}: {result.description}")
                report.append(f"    Conversation turns: {result.conversation_turns}")
                report.append(f"    Tool calls made: {attempted_all} (expected {result.expected_tool_call_count})")
                report.append(f"    LLM-initiated: {attempted_llm} | Emulated: {attempted_all - attempted_llm}")
                report.append(f"    Tool P/R/F1 (LLM): {clamp01(scn_prec)*100:.1f}% / {clamp01(scn_recall)*100:.1f}% / {clamp01(scn_f1)*100:.1f}%")
                report.append(f"    Over/Under-call factor: {scn_over_disp}")
                report.append(f"    Execution time: {result.execution_time:.2f}s")

                if result.tool_calls_made:
                    tools_summary = {}
                    for tool in result.tool_calls_made:
                        tools_summary[tool] = tools_summary.get(tool, 0) + 1
                    report.append(f"    Tools used: {', '.join(f'{k}({v})' for k, v in tools_summary.items())}")

                if result.error:
                    # Replace Unicode characters that might cause issues
                    safe_error = result.error.replace('\u2605', '*').replace('★', '*')
                    report.append(f"    Error: {safe_error}")

        # Weighted complexity score (unchanged)
        report.append("\n" + "="*60)
        report.append("FINAL SCORE")
        report.append("="*60)

        weights = {"Simple": 0.2, "Medium": 0.25, "Large": 0.3, "Extra Large": 0.25}
        weighted_score = 0.0
        weight_total = 0.0
        for suite in suites:
            for k, w in weights.items():
                if k.lower() in suite.name.lower():
                    weighted_score += suite.success_rate * w
                    weight_total += w
                    break
        if weight_total == 0:
            weighted_score = success_rate  # fallback
        else:
            weighted_score = weighted_score / weight_total

        # Use bounded metrics only
        overall_score = (
            clamp01(success_rate/100.0) +
            clamp01(f1) +  # already in 0..1
            clamp01(param_success_rate_llm/100.0) +
            clamp01(execution_success_rate_llm/100.0) +
            clamp01(semantic_match_rate_llm/100.0)
        ) / 5.0 * 100.0

        # Optional over-call penalty (soft)
        penalty = 1.0
        if overcall_factor > 1.0:
            # dampen by log so small over-calls don't tank the score
            penalty = 1.0 / (1.0 + 0.25 * max(0.0, (overcall_factor - 1.0)))
        overall_score *= penalty

        def grade(score):
            if score >= 90: return "A+"
            if score >= 85: return "A"
            if score >= 80: return "A-"
            if score >= 75: return "B+"
            if score >= 70: return "B"
            if score >= 65: return "B-"
            if score >= 60: return "C+"
            if score >= 55: return "C"
            if score >= 50: return "C-"
            if score >= 40: return "D"
            return "F"

        report.append(f"Scenario Success Rate: {success_rate:.1f}%")
        report.append(f"Tool Precision (LLM only): {clamp01(precision)*100:.1f}%")
        report.append(f"Tool Recall (LLM only): {clamp01(recall)*100:.1f}%")
        report.append(f"Tool F1 (LLM only): {clamp01(f1)*100:.1f}%")
        report.append(f"Parameter Accuracy (structural): {param_success_rate:.1f}%")
        report.append(f"Parameter Accuracy (LLM only): {param_success_rate_llm:.1f}%")
        report.append(f"Parameter Accuracy (semantic): {semantic_match_rate:.1f}%")
        report.append(f"Parameter Accuracy (semantic, LLM only): {semantic_match_rate_llm:.1f}%")
        report.append(f"Execution Success Rate: {execution_success_rate:.1f}%")
        report.append(f"Execution Success Rate (LLM only): {execution_success_rate_llm:.1f}%")
        report.append(f"Weighted Complexity Score: {weighted_score:.1f}%")
        report.append("")
        report.append(f"OVERALL SCORE: {overall_score:.1f}% ({grade(overall_score)})")
        report.append("\n" + "="*60)
        return "\n".join(report)



# ------------------------ CLI ------------------------

def main():
    parser = argparse.ArgumentParser(
        description="Test LLM natural tool calling capabilities",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --api-base https://api.openai.com/v1 --api-key sk-xxx --model gpt-4o
  %(prog)s --api-base http://localhost:8000/v1 --api-key local --model llama-70b
  %(prog)s --api-base https://openrouter.ai/api/v1 --api-key sk-or-xxx --model anthropic/claude-3.5-sonnet --provider Anthropic
        """
    )
    parser.add_argument("--api-base", required=True, help="API base URL (e.g., https://api.openai.com/v1)")
    parser.add_argument("--api-key", required=True, help="API key for authentication")
    parser.add_argument("--model", required=True, help="Model name to test")
    parser.add_argument("--provider", help="OpenRouter provider (e.g., 'Anthropic', 'OpenAI')")
    parser.add_argument("--temperature", type=float, help="Temperature for text generation (default: 0.1)")
    parser.add_argument("--reasoning-effort", choices=['low', 'medium', 'high'], help="OpenRouter reasoning effort level")
    parser.add_argument("--max-tools", type=int, default=40, help="Maximum number of tool calls to test (default: 40)")
    parser.add_argument("--output", help="Output file for the report (optional)")
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    parser.add_argument("--quick", action="store_true", help="Run only simple tests (faster)")
    args = parser.parse_args()

    print(f"\nInitializing Natural Tool Calling Tester...")
    print(f"API Base: {args.api_base}")
    print(f"Model: {args.model}")
    if args.provider:
        print(f"Provider: {args.provider}")
    if args.temperature is not None:
        print(f"Temperature: {args.temperature}")
    if args.reasoning_effort:
        print(f"Reasoning Effort: {args.reasoning_effort}")
    print(f"Max Tools: {args.max_tools}")

    tester = LLMToolTester(
        args.api_base, 
        args.api_key, 
        args.model, 
        args.debug,
        provider=args.provider,
        temperature=args.temperature,
        reasoning_effort=args.reasoning_effort
    )
    suites: List[TestSuite] = []

    all_scenarios = TestScenarios.get_scenarios()

    if args.quick:
        print("\nRunning Quick Test Suite (1-5 tools)...")
        simple_scenarios = [s for s in all_scenarios if len(s["expected_tools"]) + len(s.get("follow_ups", [])) <= 5]
        suite = tester.run_test_suite("Quick Tests (1-5 tools)", simple_scenarios[:3])
        suites.append(suite)
    else:
        if args.max_tools >= 5:
            print("\nRunning Simple Test Suite (1-5 tools)...")
            simple_scenarios = TestScenarios.get_scenario_by_complexity(1, 5)
            suite = tester.run_test_suite("Simple Tests (1-5 tools)", simple_scenarios)
            suites.append(suite)

        if args.max_tools >= 10:
            print("\nRunning Medium Test Suite (6-10 tools)...")
            medium_scenarios = TestScenarios.get_scenario_by_complexity(6, 10)
            suite = tester.run_test_suite("Medium Tests (6-10 tools)", medium_scenarios)
            suites.append(suite)

        if args.max_tools >= 20:
            print("\nRunning Large Test Suite (11-20 tools)...")
            large_scenarios = TestScenarios.get_scenario_by_complexity(11, 20)
            suite = tester.run_test_suite("Large Tests (11-20 tools)", large_scenarios)
            suites.append(suite)

        if args.max_tools >= 40:
            print("\nRunning Extra Large Test Suite (21+ tools)...")
            xl_scenarios = TestScenarios.get_scenario_by_complexity(21, 50)
            suite = tester.run_test_suite("Extra Large Tests (21+ tools)", xl_scenarios)
            suites.append(suite)

    report = tester.generate_report(suites)
    print(report)

    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\nReport saved to: {args.output}")

    overall_success = sum(sum(1 for r in suite.results if r.success) for suite in suites)
    overall_total = sum(len(suite.results) for suite in suites)
    success_rate = (overall_success / overall_total * 100) if overall_total > 0 else 0
    return 0 if success_rate >= 70 else 1


if __name__ == "__main__":
    sys.exit(main())
