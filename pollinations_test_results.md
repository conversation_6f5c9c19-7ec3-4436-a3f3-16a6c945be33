# Результаты тестирования Pollinations

## Информация о тестировании

**Провайдер:** Pollinations
**API:** `https://text.pollinations.ai/openai`
**Тестер:** LLM Tool Calling Test Suite V2
**Репозиторий:** https://github.com/adamwlarson/LLMToolCallingTester
**Ограничение:** `--max-tools 5`
**Аккаунт:** Flower Seed tier (автоматический при первом входе)
**Статус:** Beta-версия с ограниченным доступом

### Методология тестирования:
- 7 сценариев tool calling (от простых до сложных)
- 5 доступных инструментов: погода, калькулятор, акции, рейсы, конвертация валют
- Оценка точности выбора инструментов и правильности параметров
- Измерение избыточности вызовов и времени выполнения

## Протестированные модели

### openai (gpt-4.1-nano)
- **Оценка:** 30.2% (F)
- **Успешность:** 3/7 тестов
- **Проблема:** 6x избыточные вызовы

### openai-large (gpt-4.1)
- **Оценка:** 30.2% (F)
- **Успешность:** 3/7 тестов
- **Проблема:** 6x избыточные вызовы

### nova-fast
- **Оценка:** 68.4% (B-)
- **Успешность:** 0/7 тестов
- **Проблема:** Ошибки API - "Expected toolResult blocks"

### openai-reasoning
- **Оценка:** 0.0% (F)
- **Успешность:** 0/7 тестов
- **Проблема:** Ошибка сервера - "Invalid URL: undefined/chat/completions"

### qwen-coder
- **Оценка:** 30.2% (F)
- **Успешность:** 3/7 тестов
- **Проблема:** 6x избыточные вызовы, очень медленная работа (17-127 сек)

### deepseek-reasoning
- **Оценка:** 0.0% (F)
- **Успешность:** 0/7 тестов
- **Проблема:** Нет поддержки tool calling + ошибки "502 Bad Gateway"

## Итог

Все протестированные модели Pollinations показали серьезные проблемы с tool calling:
- **Технические ошибки API** (nova-fast, openai-reasoning)
- **Избыточные вызовы инструментов** (openai, openai-large)
- **Нестабильная работа** на beta-платформе

### Примечания:
- Тестирование проводилось на Flower Seed tier (базовый уровень)
- Более высокие тiers могут показать лучшие результаты
- Платформа находится в beta-стадии с ограничениями
