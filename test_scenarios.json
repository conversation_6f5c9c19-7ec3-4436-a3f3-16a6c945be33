{"scenarios": [{"name": "simple_weather", "description": "Check weather in a city", "initial_prompt": "Use the get_weather tool to check the current weather in Tokyo.", "expected_tools": ["get_weather"], "follow_ups": []}, {"name": "simple_calculation", "description": "Perform a calculation", "initial_prompt": "Use the calculate tool to determine how much each person pays if a $847 restaurant bill is split among 6 people.", "expected_tools": ["calculate"], "follow_ups": []}, {"name": "simple_stock", "description": "Check stock price", "initial_prompt": "Use the get_stock_price tool to check the current price of Apple stock (AAPL).", "expected_tools": ["get_stock_price"], "follow_ups": []}, {"name": "travel_planning", "description": "Plan a trip with multiple queries", "initial_prompt": "Please use the search_flights tool to find flights from New York to Paris on March 15th, then use get_weather to check the weather in Paris.", "expected_tools": ["search_flights", "get_weather"], "follow_ups": ["Use the search_restaurants tool to find good restaurants in Paris.", "Use the search_hotels tool to find hotels for March 15-20 in Paris."]}, {"name": "currency_travel", "description": "Travel with currency conversion", "initial_prompt": "Please use convert_currency to convert $5000 USD to British pounds, then use get_weather to check the weather forecast for London.", "expected_tools": ["convert_currency", "get_weather"], "follow_ups": ["Use search_hotels to find hotels in London for next week.", "Use search_flights to find flights from Boston to London for tomorrow."]}, {"name": "business_trip", "description": "Complex business trip planning", "initial_prompt": "Use search_flights to find flights from Chicago to San Francisco for March 20th.", "expected_tools": ["search_flights"], "follow_ups": ["Use get_weather to check the weather forecast for San Francisco.", "Use search_restaurants to find Italian restaurants in San Francisco for a business dinner.", "Use set_reminder to set a reminder to pack presentation materials at 8 PM tonight.", "Use search_hotels to find hotels near the financial district for March 20-22."]}, {"name": "investment_research", "description": "Research stocks and news", "initial_prompt": "Use get_stock_price to check the current price of Microsoft stock (MSFT).", "expected_tools": ["get_stock_price"], "follow_ups": ["Use get_stock_price to check Google (GOOGL) and Amazon (AMZN) stock prices.", "Use get_news to find the latest news about artificial intelligence.", "Use calculate to determine how much to invest in each stock if splitting $10,000 equally across three stocks."]}, {"name": "international_planning", "description": "International travel with translations", "initial_prompt": "Use translate_text to translate 'Thank you for your help' to Japanese.", "expected_tools": ["translate_text"], "follow_ups": ["Use get_weather to check the weather in Tokyo for April.", "Use convert_currency to convert $2000 USD to Japanese Yen.", "Use search_flights to find flights from Los Angeles to Tokyo on April 10th.", "Use translate_text to translate 'Where is the train station?' to Japanese.", "Use search_hotels to find hotels in Shibuya district for April 10-20."]}, {"name": "event_planning", "description": "Planning an event with multiple tasks", "initial_prompt": "I'm organizing a company event in Miami. What's the weather forecast for Miami?", "expected_tools": ["get_weather"], "follow_ups": ["Search for restaurants that can accommodate 50 people", "Set a reminder to send invitations tomorrow at 10 AM", "What's the latest news about event planning trends?", "Calculate the cost if catering is $45 per person for 50 people", "Find hotels near the beach for our out-of-town guests checking in May 15th"]}, {"name": "complete_vacation", "description": "Full vacation planning with many steps", "initial_prompt": "I want to plan a complete vacation to Europe. Let's start with checking flights from New York to London on June 1st.", "expected_tools": ["search_flights"], "follow_ups": ["What's the weather like in London in June?", "Convert $5000 to British pounds", "Search for hotels in London for June 1-5", "Find good restaurants in London, preferably British cuisine", "Now check flights from London to Paris on June 5th", "What's the weather in Paris in June?", "Convert $2000 to Euros", "Search for hotels in Paris for June 5-10", "Translate 'I would like a table for two' to French", "Find French restaurants in Paris", "Check flights from Paris to Rome on June 10th", "Weather in Rome in June?", "Search for hotels in Rome for June 10-15", "Translate 'How much does this cost?' to Italian", "Set a reminder to book everything by next Friday at 5 PM"]}, {"name": "financial_portfolio", "description": "Comprehensive financial analysis", "initial_prompt": "I want to review my tech portfolio. Start by checking Apple's current price.", "expected_tools": ["get_stock_price"], "follow_ups": ["Check Microsoft stock price", "Check Google stock price", "Check Amazon stock price", "Check Tesla stock price", "Calculate the total if I have 100 shares of Apple at current price", "Calculate 50 shares of Microsoft", "Calculate 75 shares of Google", "Calculate 30 shares of Amazon", "Calculate 40 shares of Tesla", "What's the latest news about tech stocks?", "Get news about cryptocurrency", "Convert $10,000 to Euros for my European investments", "Convert $5,000 to Japanese Yen", "Set a reminder to review portfolio again next month at 3 PM", "What's the total value if I sum all my stock positions?", "Get news about the Federal Reserve", "Calculate what percentage each stock represents of my total portfolio"]}, {"name": "conference_coordination", "description": "Coordinate a multi-city conference tour", "initial_prompt": "I'm organizing a conference tour across multiple cities. First, check flights from San Francisco to Seattle on July 1st.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in Seattle in July?", "Find hotels in Seattle for July 1-3", "Search for conference venues (restaurants) that can host 100 people in Seattle", "Check flights from Seattle to Portland on July 3rd", "Weather in Portland?", "Hotels in Portland for July 3-5", "Calculate the budget: 100 people × $75 per person for catering", "Flights from Portland to Los Angeles on July 5th", "Weather in Los Angeles in July?", "Hotels in LA for July 5-7", "Get news about conference industry trends", "Convert our $50,000 budget to see how much that is in Euros", "Set reminder to confirm all venues by June 15th at noon", "Translate our welcome message 'Welcome to our annual conference' to Spanish", "Also translate it to Mandarin Chinese", "Calculate total hotel costs if average is $150/night for 20 rooms across all cities", "Search for restaurants in Los Angeles for our closing dinner", "What's the latest news about business travel?", "Set another reminder to send final attendee list on June 25th at 9 AM"]}, {"name": "global_expansion", "description": "Plan international business expansion", "initial_prompt": "We're expanding our business globally. Start by checking flights from New York to Tokyo for August 1st.", "expected_tools": ["search_flights"], "follow_ups": ["What's the weather in Tokyo in August?", "Convert $100,000 USD to Japanese Yen for initial investment", "Search for hotels in Tokyo for August 1-7", "Translate 'We look forward to doing business with you' to Japanese", "Get news about Japanese market trends", "Search for restaurants in Tokyo for business meetings", "Check flights from Tokyo to Beijing on August 7th", "Weather in Beijing in August?", "Convert $50,000 to Chinese Yuan", "Search hotels in Beijing for August 7-10", "Translate 'Thank you for your partnership' to Mandarin", "Get news about Chinese tech industry", "Search restaurants in Beijing", "Check flights from Beijing to Singapore on August 10th", "Weather in Singapore?", "Convert $75,000 to Singapore dollars", "Search hotels in Singapore for August 10-14", "Get news about Southeast Asian markets", "Search restaurants in Singapore", "Calculate total travel budget: 3 cities × 5 days × $300/day", "Set reminder to prepare presentation materials by July 25th", "Check flights from Singapore back to New York on August 14th"]}, {"name": "mega_world_tour", "description": "Plan a comprehensive world tour", "initial_prompt": "I'm planning a world tour. Start with flights from Los Angeles to Sydney on September 1st.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in Sydney in September?", "Convert $3000 to Australian dollars", "Search hotels in Sydney for September 1-4", "Translate 'Good day mate' to Australian English", "Search restaurants in Sydney", "Check flights from Sydney to Tokyo on September 4th", "Weather in Tokyo?", "Convert $2500 to Japanese Yen", "Search hotels in Tokyo for September 4-7", "Translate 'Where is the subway?' to Japanese", "Get news about Tokyo Olympics legacy", "Search restaurants in Tokyo", "Flights from Tokyo to Dubai on September 7th", "Weather in Dubai in September?", "Convert $4000 to UAE Dirhams", "Search hotels in Dubai for September 7-10", "Get news about Dubai expo", "Search restaurants in Dubai", "Flights from Dubai to Paris on September 10th", "Weather in Paris?", "Convert $3500 to Euros", "Search hotels in Paris for September 10-13", "Translate 'Where is the Eiffel Tower?' to French", "Search French restaurants in Paris", "Get news about Paris fashion week", "Flights from Paris to London on September 13th", "Weather in London?", "Convert $3000 to British pounds", "Search hotels in London for September 13-16", "Search restaurants in London", "Calculate total budget for entire trip", "Set reminder to get travel insurance by August 15th", "Flights from London back to Los Angeles on September 16th"]}, {"name": "startup_investor_roadshow", "description": "Organize investor meetings across multiple countries", "initial_prompt": "Planning an investor roadshow. Check flights from San Francisco to London on October 1st.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in London in October?", "Convert $500,000 investment fund to British pounds", "Search hotels in London financial district for October 1-3", "Search restaurants for investor dinners in London", "Get news about UK startup ecosystem", "Set reminder for pitch deck review on September 28th at 2 PM", "Flights from London to Berlin on October 3rd", "Weather in Berlin?", "Convert $250,000 to Euros for German investments", "Search hotels in Berlin for October 3-5", "Translate 'We see great potential in your startup' to German", "Search restaurants in Berlin", "Get news about European tech funding", "Flights from Berlin to Stockholm on October 5th", "Weather in Stockholm?", "Convert $150,000 to Swedish Krona", "Search hotels in Stockholm for October 5-7", "Translate 'Innovation is key to success' to Swedish", "Search restaurants in Stockholm", "Get news about Nordic startup scene", "Calculate ROI if we invest $50,000 in 10 startups with 20% expected return", "Flights from Stockholm to Amsterdam on October 7th", "Weather in Amsterdam?", "Search hotels in Amsterdam for October 7-9", "Search restaurants in Amsterdam", "Set reminder to send investment term sheets by October 15th", "Get news about Dutch fintech sector", "Calculate total travel expenses for tax deduction", "Flights from Amsterdam back to San Francisco on October 9th"]}, {"name": "academic_conference_circuit", "description": "Attend multiple academic conferences worldwide", "initial_prompt": "I'm attending academic conferences globally. Check flights from Boston to Oxford on November 1st.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in Oxford in November?", "Search hotels near Oxford University for November 1-3", "Convert $2000 conference budget to British pounds", "Search restaurants in Oxford", "Get news about latest research in artificial intelligence", "Set reminder to submit paper by October 20th at midnight", "Flights from London to Geneva on November 3rd", "Weather in Geneva?", "Convert $1500 to Swiss Francs", "Search hotels in Geneva for November 3-5", "Translate 'Where is the conference center?' to French", "Search restaurants near CERN in Geneva", "Get news about particle physics breakthroughs", "Flights from Geneva to Vienna on November 5th", "Weather in Vienna?", "Convert $1200 to Euros", "Search hotels in Vienna for November 5-7", "Translate 'Thank you for the invitation' to German", "Search restaurants in Vienna", "Calculate conference registration fees: 5 conferences × $300 each", "Get news about quantum computing research", "Flights from Vienna to Prague on November 7th", "Weather in Prague?", "Search hotels in Prague for November 7-9", "Search restaurants in Prague", "Set reminder to prepare presentation slides by October 25th", "Flights from Prague to Barcelona on November 9th", "Weather in Barcelona?", "Search hotels in Barcelona for November 9-11", "Translate 'See you at the conference' to Spanish", "Search restaurants in Barcelona", "Get news about European research grants", "Calculate total publication costs if submitting to 3 journals at $500 each", "Flights from Barcelona back to Boston on November 11th"]}, {"name": "luxury_shopping_expedition", "description": "Plan a luxury shopping tour across fashion capitals", "initial_prompt": "Planning a luxury shopping tour. Check flights from New York to Milan for December 1st.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in Milan in December?", "Convert $50,000 shopping budget to Euros", "Search luxury hotels in Milan for December 1-3", "Search Michelin-starred restaurants in Milan", "Get news about Milan Fashion Week", "Calculate VAT refund on $10,000 purchase in Italy", "Set reminder to check credit card limits by November 25th", "Flights from Milan to Paris on December 3rd", "Weather in Paris?", "Search hotels near Champs-Élysées for December 3-5", "Translate 'Do you have this in another size?' to French", "Search restaurants in Paris 8th arrondissement", "Convert additional $30,000 to Euros", "Get news about French luxury brands", "Calculate savings if items are 20% cheaper in Europe", "Flights from Paris to London on December 5th", "Weather in London?", "Convert $25,000 to British pounds", "Search hotels in Mayfair for December 5-7", "Search restaurants near Bond Street", "Get news about British fashion designers", "Set reminder to declare customs on return", "Flights from London to Dubai on December 7th", "Weather in Dubai?", "Convert $40,000 to UAE Dirhams", "Search hotels in Dubai Mall area for December 7-9", "Search restaurants in Dubai", "Get news about Dubai Shopping Festival", "Calculate duty-free savings on jewelry purchases", "Flights from Dubai to Tokyo on December 9th", "Weather in Tokyo?", "Convert $35,000 to Japanese Yen", "Search hotels in Ginza for December 9-11", "Translate 'Is this authentic?' to Japanese", "Search restaurants in Ginza", "Get news about Japanese fashion trends", "Calculate total spent across all cities", "Flights from Tokyo back to New York on December 11th"]}, {"name": "film_festival_circuit", "description": "Attend major film festivals worldwide", "initial_prompt": "I'm attending film festivals globally. Start with flights from Los Angeles to Cannes for May 15th.", "expected_tools": ["search_flights"], "follow_ups": ["Weather in Cannes in May?", "Convert $15,000 budget to Euros", "Search hotels on the Croisette for May 15-20", "Search restaurants in Cannes", "Get news about Cannes Film Festival lineup", "Set reminder for screening schedule on May 14th", "Flights from Nice to Venice on May 20th", "Weather in Venice?", "Search hotels near the Lido for May 20-25", "Translate 'Where is the festival venue?' to Italian", "Search restaurants in Venice", "Get news about Venice Biennale", "Calculate accommodation costs: 10 nights × $400/night", "Flights from Venice to Berlin on May 25th", "Weather in Berlin?", "Convert $8,000 to Euros for Berlin expenses", "Search hotels near Potsdamer Platz for May 25-30", "Translate 'Congratulations on your film' to German", "Search restaurants in Berlin", "Get news about Berlinale special screenings", "Flights from Berlin to Toronto on May 30th", "Weather in Toronto?", "Convert $10,000 to Canadian dollars", "Search hotels downtown Toronto for May 30-June 4", "Search restaurants in Toronto Entertainment District", "Get news about TIFF year-round programming", "Set reminder to submit film for next year's festivals", "Calculate total travel insurance needed", "Flights from Toronto to Sundance (via Salt Lake City) on June 4th", "Weather in Park City?", "Convert $12,000 to USD for Sundance", "Search hotels in Park City for June 4-9", "Search restaurants on Main Street Park City", "Get news about independent film funding", "Calculate potential distribution deal value", "Flights from Salt Lake City to Tokyo on June 9th", "Weather in Tokyo?", "Convert $7,000 to Japanese Yen", "Search hotels in Roppongi for June 9-14", "Translate 'Thank you for watching our film' to Japanese", "Search restaurants in Roppongi", "Get news about Asian film market", "Set reminder to follow up with distributors", "Flights from Tokyo back to Los Angeles on June 14th"]}]}