# LLM Tool Calling Test Suite V2

A comprehensive CLI tool for testing function calling capabilities of LLMs via the OpenAI API standard, with special support for OpenRouter provider routing.

## IMPORTANT
- Semantic accuracy is too early to use as an indicator
- Structural accuracy and Tool Recall are the primary metrics to look at currently

### Example Output

```
Scenario Success Rate: 44.4%
├─ Light indicator, higher is better

Tool Precision (LLM only): 46.1%
├─ Measure of how often the correct tool is called
├─ Higher is better, decent indicator with current implementations

Tool Recall (LLM only): 54.2%
├─ Expected Tools vs Actual Tools Called
├─ Higher is better, good indicator

Tool F1 (LLM only): 49.8%
├─ Combination of precision and recall

Parameter Accuracy (structural): 100.0%
├─ Shape of tool calls is correct

Parameter Accuracy (LLM only): 100.0%
├─ Future feature - split emulated tool calls (can ignore)

Parameter Accuracy (semantic): 46.1%
├─ Too early to use as an indicator, but can be a data point
├─ Checks actual values passed into the tool

Parameter Accuracy (semantic, LLM only): 46.1%
├─ Future feature - split emulated tool calls (can ignore)

Execution Success Rate: 100.0%
Execution Success Rate (LLM only): 100.0%

Weighted Complexity Score: 46.6%
├─ Depends on complexity of the scenario
├─ Higher is better

═══════════════════════════════════════════════════════════════
OVERALL SCORE: 65.2% (B-)
├─ Use as overall indicator
└─ Look at nuance as each value matters differently
```


## Features

- Natural conversation-based tool calling scenarios
- Tests from simple (1-5 tools) to complex (40+ tools) scenarios
- Supports any OpenAI-compatible API endpoint including OpenRouter
- Provider-specific routing for optimal performance
- Detailed metrics: precision, recall, F1 scores, and semantic accuracy
- Automatic capability detection for different server implementations
- Batch testing scripts for comparing models and providers

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

```bash
python tool_tester_v2.py --api-base <API_URL> --api-key <API_KEY> --model <MODEL_NAME>
```

### Examples

Test OpenAI GPT-4:
```bash
python tool_tester_v2.py --api-base https://api.openai.com/v1 --api-key sk-xxx --model gpt-4o
```

Test via OpenRouter with provider routing:
```bash
python tool_tester_v2.py --api-base https://openrouter.ai/api/v1 --api-key sk-or-xxx --model anthropic/claude-3.5-sonnet --provider Anthropic
```

Test local model:
```bash
python tool_tester_v2.py --api-base http://localhost:8000/v1 --api-key local --model llama-70b
```

Quick test mode (faster, fewer scenarios):
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --quick
```

Save report to file:
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --output report.txt
```

Limit maximum tool calls:
```bash
python tool_tester_v2.py --api-base <URL> --api-key <KEY> --model <MODEL> --max-tools 20
```

### Using the Generic Batch Testing Script

The `test_generic_model.sh` script allows you to easily test any model across multiple providers:

1. **Edit the script configuration** at the top of the file:
   ```bash
   # Edit these variables in test_generic_model.sh:
   MODEL="openai/gpt-4o"           # Your model
   TEMPERATURE=0.1                  # Temperature setting
   providers=(                      # List of providers to test
       "openai"
       "anthropic"
       "fireworks"
   )
   ```

2. **Run the script**:
   ```bash
   # Quick tests (fewer scenarios)
   ./test_generic_model.sh YOUR_API_KEY quick
   
   # Full test suite
   ./test_generic_model.sh YOUR_API_KEY full
   ```

3. **Results** will be saved in a timestamped directory with:
   - Individual test results for each provider
   - Summary report with scores
   - CSV file for easy analysis
   - Sorted leaderboard of providers by performance

### Provider-Specific Testing

Some example provider configurations:
```bash
# Test with specific precision modes
--provider "fireworks/fp8"
--provider "deepinfra/fp4"
--provider "together/fp8"

# Test with reasoning effort (for supported models)
--reasoning-effort high

# Adjust temperature for testing
--temperature 0.0  # Most deterministic
--temperature 0.7  # More creative
```

## Test Categories

1. **Simple Tests (1-5 tools)**: Basic single and multi-tool scenarios
2. **Medium Tests (6-10 tools)**: Moderate complexity with follow-ups
3. **Large Tests (11-20 tools)**: Complex multi-step operations
4. **Extra Large Tests (21+ tools)**: Comprehensive workflow simulations

## Available Tools

The test suite includes 10 different tool types:
- `get_weather`: Weather information for locations
- `calculate`: Mathematical calculations
- `search_flights`: Flight search between cities
- `search_hotels`: Hotel availability search
- `search_restaurants`: Restaurant recommendations
- `translate_text`: Language translation
- `get_stock_price`: Stock market prices
- `convert_currency`: Currency conversion
- `get_news`: News articles on topics
- `set_reminder`: Create reminders

## Metrics Explained

- **Scenario Success Rate**: Percentage of test scenarios completed successfully
- **Tool Precision**: When the AI calls a tool, how often it's the correct one
- **Tool Recall**: Percentage of required tools that were actually called
- **Tool F1 Score**: Harmonic mean of precision and recall
- **Parameter Accuracy (structural)**: Correct format and required fields
- **Parameter Accuracy (semantic)**: Correct values for the context
- **Execution Success Rate**: Tools that executed without errors
- **Weighted Complexity Score**: Performance adjusted for scenario difficulty

## Success Criteria

- Individual tests pass if ≥70% of expected tools are called correctly
- Structural parameters must be ≥60% correct
- Overall grade based on combined metrics (A+ = 90%+, A = 85%+, etc.)

## Output

The tool generates a comprehensive report showing:
- Overall success rate and letter grade
- Detailed metrics (precision, recall, F1)
- Per-scenario breakdowns
- Tool call details and emulation statistics
- Execution times
- Error details and conversation logs

## Test Scenarios

Test scenarios are defined in `test_scenarios.json` and include:
- Travel planning
- Investment research
- Event coordination
- Academic conferences
- Shopping expeditions
- And many more real-world use cases