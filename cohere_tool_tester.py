#!/usr/bin/env python3
"""
Cohere Tool Calling Test Suite
Адаптированная версия для тестирования Cohere command-a-03-2025
"""

import json
import sys
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

import cohere
from tool_definitions import ToolDefinitions

# Fix Windows console encoding issues
if sys.platform == 'win32':
    sys.stdout = sys.__stdout__
    sys.stderr = sys.__stderr__

@dataclass
class ToolCallResult:
    """Store results for a single tool call"""
    tool_name: str
    expected: bool
    parameters_correct: bool
    execution_successful: bool
    semantic_match: bool = True
    llm_initiated: bool = True

@dataclass
class TestResult:
    """Store results for a complete test scenario"""
    scenario_name: str
    success: bool
    tool_calls: List[ToolCallResult]
    conversation_turns: int
    execution_time: float
    error_message: Optional[str] = None

class CohereToolTester:
    """Cohere-specific tool calling tester"""
    
    def __init__(self):
        self.api_key = "FEBYH8H2ogbtTRaBmChNCaz3aMnmE3EtBkNQWYv5"
        self.model = "command-r-plus"
        self.client = cohere.ClientV2(api_key=self.api_key)
        
        # Get 5 tools for testing
        all_tools = ToolDefinitions.get_all_tools()
        self.tools = all_tools[:5]
        self.debug = True
        
        print(f"🚀 Initialized Cohere Tester")
        print(f"   Model: {self.model}")
        print(f"   Tools: {len(self.tools)}")
        
    def _convert_tools_to_cohere(self) -> List[Dict[str, Any]]:
        """Convert OpenAI format tools to Cohere format"""
        cohere_tools = []
        for tool in self.tools:
            if tool.get("type") == "function":
                func = tool["function"]
                cohere_tools.append({
                    "type": "function",
                    "function": {
                        "name": func["name"],
                        "description": func["description"],
                        "parameters": func["parameters"]
                    }
                })
        return cohere_tools
    
    def _convert_messages_to_cohere(self, messages: List[Dict]) -> List[Dict]:
        """Convert OpenAI format messages to Cohere format"""
        cohere_messages = []
        for msg in messages:
            if msg["role"] == "system":
                # Cohere doesn't have system role, add to first user message
                continue
            elif msg["role"] == "user":
                cohere_messages.append({
                    "role": "user",
                    "content": [{"type": "text", "text": msg["content"]}]
                })
            elif msg["role"] == "assistant":
                cohere_messages.append({
                    "role": "assistant", 
                    "content": [{"type": "text", "text": msg["content"]}]
                })
        return cohere_messages
    
    def _make_cohere_request(self, messages: List[Dict], tools: List[Dict] = None) -> Any:
        """Make request to Cohere API"""
        cohere_messages = self._convert_messages_to_cohere(messages)
        
        # Add system message content to first user message if exists
        system_content = ""
        for msg in messages:
            if msg["role"] == "system":
                system_content = msg["content"] + "\n\n"
                break
        
        if cohere_messages and system_content:
            first_user = cohere_messages[0]
            if first_user["role"] == "user":
                first_user["content"][0]["text"] = system_content + first_user["content"][0]["text"]
        
        kwargs = {
            "model": self.model,
            "messages": cohere_messages,
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        if tools:
            cohere_tools = self._convert_tools_to_cohere()
            kwargs["tools"] = cohere_tools
        
        if self.debug:
            print(f"🔄 Cohere Request:")
            print(f"   Messages: {len(cohere_messages)}")
            print(f"   Tools: {len(tools) if tools else 0}")
        
        try:
            response = self.client.chat(**kwargs)
            return response
        except Exception as e:
            print(f"❌ Cohere API Error: {e}")
            raise
    
    def _extract_tool_calls(self, response) -> List[Dict]:
        """Extract tool calls from Cohere response"""
        tool_calls = []
        
        if hasattr(response, 'message') and hasattr(response.message, 'tool_calls'):
            for tool_call in response.message.tool_calls:
                if hasattr(tool_call, 'function'):
                    tool_calls.append({
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    })
        
        return tool_calls
    
    def run_simple_test(self, tool_name: str, user_prompt: str) -> TestResult:
        """Run a simple single-tool test"""
        print(f"\n🧪 Testing: {tool_name}")
        start_time = time.time()
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant. Use the available tools when appropriate."},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_cohere_request(messages, self.tools)
            tool_calls = self._extract_tool_calls(response)
            
            execution_time = time.time() - start_time
            
            # Analyze results
            expected_calls = 1
            actual_calls = len(tool_calls)
            
            tool_results = []
            for call in tool_calls:
                tool_results.append(ToolCallResult(
                    tool_name=call["function"]["name"],
                    expected=call["function"]["name"] == tool_name,
                    parameters_correct=True,  # Simplified for now
                    execution_successful=True,
                    semantic_match=True,
                    llm_initiated=True
                ))
            
            success = (actual_calls > 0 and 
                      any(r.tool_name == tool_name for r in tool_results))
            
            print(f"   ✅ Completed: {actual_calls} tool calls in {execution_time:.2f}s")
            if tool_calls:
                for call in tool_calls:
                    print(f"      - {call['function']['name']}")
            
            return TestResult(
                scenario_name=f"simple_{tool_name}",
                success=success,
                tool_calls=tool_results,
                conversation_turns=1,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"   ❌ Failed: {e}")
            
            return TestResult(
                scenario_name=f"simple_{tool_name}",
                success=False,
                tool_calls=[],
                conversation_turns=1,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def run_test_suite(self) -> List[TestResult]:
        """Run the complete test suite"""
        print("🚀 Starting Cohere Tool Calling Test Suite")
        print("=" * 60)
        
        test_cases = [
            ("get_weather", "Use the get_weather tool to check the weather in Tokyo."),
            ("calculate", "Use the calculate tool to compute 15 * 23."),
            ("get_stock_price", "Use the get_stock_price tool to get the price of AAPL stock."),
            ("search_flights", "Use the search_flights tool to find flights from NYC to LAX on 2025-03-15."),
            ("convert_currency", "Use the convert_currency tool to convert 100 USD to EUR.")
        ]
        
        results = []
        for tool_name, prompt in test_cases:
            result = self.run_simple_test(tool_name, prompt)
            results.append(result)
            time.sleep(1)  # Rate limiting
        
        return results
    
    def generate_report(self, results: List[TestResult]) -> str:
        """Generate test report"""
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.success)
        total_tool_calls = sum(len(r.tool_calls) for r in results)
        total_time = sum(r.execution_time for r in results)
        
        report = f"""
============================================================
COHERE TOOL CALLING TEST REPORT
============================================================
Model: {self.model}
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL SUMMARY
----------------------------------------
Total Scenarios: {total_tests}
Successful Scenarios: {successful_tests}
Failed Scenarios: {total_tests - successful_tests}
Scenario Success Rate: {(successful_tests/total_tests)*100:.1f}%

TOOL CALL STATISTICS
-------------------------
Total Tool Calls: {total_tool_calls}
Average Execution Time: {total_time/total_tests:.2f}s

SCENARIO DETAILS
----------------------------------------
"""
        
        for result in results:
            status = "PASS" if result.success else "FAIL"
            report += f"\n  [{status}] {result.scenario_name}\n"
            report += f"    Tool calls: {len(result.tool_calls)}\n"
            report += f"    Execution time: {result.execution_time:.2f}s\n"
            if result.error_message:
                report += f"    Error: {result.error_message}\n"
            if result.tool_calls:
                tools_used = [tc.tool_name for tc in result.tool_calls]
                report += f"    Tools used: {', '.join(tools_used)}\n"
        
        # Final score
        score = (successful_tests / total_tests) * 100
        if score >= 90:
            grade = "A+"
        elif score >= 80:
            grade = "A"
        elif score >= 70:
            grade = "B"
        elif score >= 60:
            grade = "C"
        else:
            grade = "F"
        
        report += f"\n============================================================\n"
        report += f"FINAL SCORE: {score:.1f}% ({grade})\n"
        report += f"============================================================\n"
        
        return report

def main():
    """Main test runner"""
    tester = CohereToolTester()
    results = tester.run_test_suite()
    report = tester.generate_report(results)
    print(report)
    
    # Save report
    with open(f"cohere_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("📄 Report saved to file")

if __name__ == "__main__":
    main()
