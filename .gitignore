# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv
pip-log.txt
pip-delete-this-directory.txt

# Test results directories
tool_test*/
tool_test_results*/
deepseek_comparison*/
deepseek_fireworks_comparison*/
test_results*/

# Log files
*.log

# CSV and result files
results.csv
summary.txt
failed_providers.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
desktop.ini

# Temporary files
*.tmp
*.bak
*.backup
bash.exe.stackdump

# API keys (never commit these!)
.env
*.key
api_keys.txt
config.ini

# Jupyter
.ipynb_checkpoints/
*.ipynb

.claude
__pycache__