
============================================================
COHERE TOOL CALLING TEST REPORT
============================================================
Model: command-r-plus
Timestamp: 2025-08-24 20:02:17

OVERALL SUMMARY
----------------------------------------
Total Scenarios: 5
Successful Scenarios: 4
Failed Scenarios: 1
Scenario Success Rate: 80.0%

TOOL CALL STATISTICS
-------------------------
Total Tool Calls: 4
Average Execution Time: 9.12s

SCENARIO DETAILS
----------------------------------------

  [PASS] simple_get_weather
    Tool calls: 1
    Execution time: 11.26s
    Tools used: get_weather

  [PASS] simple_calculate
    Tool calls: 1
    Execution time: 5.08s
    Tools used: calculate

  [PASS] simple_get_stock_price
    Tool calls: 1
    Execution time: 4.22s
    Tools used: get_stock_price

  [PASS] simple_search_flights
    Tool calls: 1
    Execution time: 11.81s
    Tools used: search_flights

  [FAIL] simple_convert_currency
    Tool calls: 0
    Execution time: 13.21s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-api-warning': "model 'command-r-plus' is deprecated and will be removed September 15 2025. Please consider upgrading to a newer model to avoid future service disruptions", 'x-debug-trace-id': '2e202abc9df4ab5fea39a1f806084ae6', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '3', 'date': 'Sun, 24 Aug 2025 17:02:17 GMT', 'content-length': '189', 'x-envoy-upstream-service-time': '12980', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 422, body: {'id': '4c288301-5103-4e9b-a0a6-281955b3174c', 'message': 'all generated tool calls were hallucinated. Try updating tool definitions or use `strict_tools` if it is compatible with this model'}

============================================================
FINAL SCORE: 80.0% (A)
============================================================
