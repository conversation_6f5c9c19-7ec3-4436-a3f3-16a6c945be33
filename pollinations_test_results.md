# Результаты тестирования провайдера Pollinations

## Информация о тесте

**Провайдер:** Pollinations  
**API:** `https://text.pollinations.ai/openai`  
**Модель:** `openai` (gpt-4.1-nano)  
**Дата:** 2025-08-24 23:10:44  
**Ограничение:** `--max-tools 5`

---

## 📊 Общие результаты

| Метрика | Значение |
|---------|----------|
| **Общая оценка** | **30.2% (F)** |
| **Успешность сценариев** | 42.9% (3/7) |
| **Точность инструментов** | 8.3% |
| **Полнота инструментов** | 33.3% |
| **F1 оценка** | 13.3% |
| **Коэффициент избыточности** | x4.00 |
| **Семантическая точность** | 8.3% |

---

## ✅ Успешные тесты

### 1. simple_weather - Проверка погоды
- **Статус:** ✅ PASS
- **Вызовы:** 6 (ожидалось 1)
- **Время:** 10.04s
- **Инструмент:** get_weather(6)
- **Проблема:** Избыточные вызовы

### 2. simple_calculation - Вычисления
- **Статус:** ✅ PASS
- **Вызовы:** 6 (ожидалось 1)
- **Время:** 9.35s
- **Инструмент:** calculate(6)
- **Проблема:** Избыточные вызовы

### 3. simple_stock - Цена акций
- **Статус:** ✅ PASS
- **Вызовы:** 6 (ожидалось 1)
- **Время:** 8.89s
- **Инструмент:** get_stock_price(6)
- **Проблема:** Избыточные вызовы

---

## ❌ Провальные тесты

### 4. travel_planning - Планирование поездки
- **Статус:** ❌ FAIL
- **Вызовы:** 14 (ожидалось 4)
- **Время:** 20.40s
- **Инструмент:** get_weather(14)
- **Проблема:** Только погода, никаких рейсов/отелей

### 5. currency_travel - Поездка с валютой
- **Статус:** ❌ FAIL
- **Вызовы:** 14 (ожидалось 4)
- **Время:** 20.50s
- **Инструмент:** get_weather(14)
- **Проблема:** Только погода, никакой конвертации

### 6. business_trip - Бизнес поездка
- **Статус:** ❌ FAIL
- **Вызовы:** 20 (ожидалось 5)
- **Время:** 30.72s
- **Инструмент:** search_flights(20)
- **Проблема:** Только рейсы, ничего больше

### 7. investment_research - Инвестиционные исследования
- **Статус:** ❌ FAIL
- **Вызовы:** 18 (ожидалось 5)
- **Время:** 25.27s
- **Инструмент:** get_stock_price(18)
- **Проблема:** Только акции, никаких новостей

---

## 🔍 Анализ проблем

### Основные проблемы:
1. **Критическая избыточность** - модель вызывает один инструмент многократно
2. **Застревание на одном инструменте** - не использует разнообразие
3. **Низкая семантическая точность** - 8.3%
4. **Неэффективность** - все задачи за 1 ход диалога

### Паттерн поведения:
- **Простые задачи:** 6 вызовов одного инструмента
- **Сложные задачи:** 14-20 вызовов одного инструмента
- **Отсутствие логики остановки**

---

## 📈 Сравнение с другими провайдерами

| Провайдер | Модель | Оценка | Избыточность |
|-----------|--------|--------|--------------|
| **Together.xyz** | Qwen3-Coder-480B | **95.6% (A+)** | x0.95 |
| **Cohere** | command-a-03-2025 | **80.0% (A)** | Нет |
| **g4f** | default | **68.4% (B-)** | x0.43 |
| **Gemma** | 3n-E4B | **45.2% (D)** | x1.43 |
| **Локальный** | gpt-4.1 | **30.2% (F)** | x4.00 |
| **Pollinations** | openai | **30.2% (F)** | x4.00 |

---

## 💡 Выводы

### ❌ Не рекомендуется для:
- Продакшн-систем с tool calling
- Сложных многошаговых задач
- Систем, требующих точности

### ⚠️ Возможно для:
- Простых однозадачных сценариев
- Экспериментов и тестирования
- Случаев, где избыточность не критична

### 🔧 Проблемы провайдера:
- Та же проблема, что у локального gpt-4.1
- Возможно, использует ту же базовую модель
- Нужна настройка логики остановки

---

## 🎯 Рекомендации

1. **Избегать Pollinations** для серьезных tool calling проектов
2. **Использовать проверенные провайдеры** (Together.xyz, Cohere)
3. **Тестировать с другими моделями** на Pollinations
4. **Настроить параметры** для уменьшения избыточности

---

*Тест проведен с использованием LLM Tool Calling Test Suite V2*
