# Сравнительный анализ LLM моделей для Tool Calling

## Обзор тестирования

Проведено тестирование 5 различных LLM моделей на способность к вызову инструментов (tool calling) с использованием набора тестов LLM Tool Calling Test Suite V2.

### Параметры тестирования
- **Ограничение инструментов:** `--max-tools 5`
- **Категория тестов:** Simple Test Suite (1-5 инструментов)
- **Количество сценариев:** 7
- **Дата тестирования:** 2025-08-24

---

## 🏆 Рейтинг моделей

| Место | Модель | Общая оценка | Успешность | Точность | Полнота | F1 |
|-------|--------|--------------|------------|----------|---------|-----|
| 🥇 | **Qwen3-Coder-480B** | **95.6% (A+)** | 100.0% | 90.0% | 85.7% | 87.8% |
| 🥈 | **g4ffree (default)** | **68.4% (B-)** | 0.0%* | 88.9% | 38.1% | 53.3% |
| 🥉 | **Gemma-3n-E4B** | **45.2% (D)** | 28.6% | 10.0% | 14.3% | 11.8% |
| 4 | **gpt-4.1** | **30.2% (F)** | 42.9% | 8.3% | 33.3% | 13.3% |
| 5 | **Qwen3-235B-Thinking** | **24.9% (F)** | 42.9% | 6.2% | 33.3% | 10.5% |
| 6 | **GLM-4.5-Air-FP8** | **0.0% (F)** | 0.0% | 0.0% | 0.0% | 0.0% |

*\* g4ffree показал 0% успешности из-за технических ошибок API, но продемонстрировал отличное понимание задач*

---

## 📊 Детальные результаты

### 🥇 1. Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8

**API:** `https://api.together.xyz/v1`  
**Результат:** 🏆 **ЛУЧШАЯ МОДЕЛЬ**

#### Метрики:
- ✅ **Общая оценка:** 95.6% (A+)
- ✅ **Успешность сценариев:** 100% (7/7)
- ✅ **Точность инструментов:** 90.0%
- ✅ **Полнота инструментов:** 85.7%
- ✅ **Семантическая точность:** 90.0%
- ✅ **Коэффициент избыточности:** x0.95 (почти идеально)

#### Особенности:
- **Идеальное поведение:** Правильное количество вызовов инструментов
- **Разнообразие:** Использует разные типы инструментов для комплексных задач
- **Эффективность:** 2-10 ходов диалога в зависимости от сложности
- **Стабильность:** Никаких избыточных или ошибочных вызовов

#### Примеры успешной работы:
```
travel_planning: search_flights(1), get_weather(1), search_restaurants(1), search_hotels(1)
business_trip: search_flights(1), get_weather(1), search_restaurants(1), set_reminder(1), search_hotels(1)
```

---

### 🥈 2. g4ffree (default) - Локальный проект

**API:** `http://127.0.0.1:1337/v1`  
**Результат:** 🎯 **ВЫСОКИЙ ПОТЕНЦИАЛ**

#### Метрики:
- ⚠️ **Общая оценка:** 68.4% (B-)
- ❌ **Успешность сценариев:** 0% (технические ошибки)
- ✅ **Точность инструментов:** 88.9%
- ⚠️ **Полнота инструментов:** 38.1%
- ✅ **Семантическая точность:** 88.9%
- ✅ **Коэффициент избыточности:** x0.43 (консервативный)

#### Особенности:
- **Отличное понимание:** Правильно выбирает инструменты
- **Консервативность:** Не делает избыточных вызовов
- **Техническая проблема:** Все вызовы завершаются ошибкой `400 Bad Request`
- **Потенциал:** При исправлении API может показать отличные результаты

#### Критическая проблема:
```
Error: ResponseError: Error 400: 400 Bad Request
```

---

### 🥉 3. google/gemma-3n-E4B-it

**API:** `https://api.together.xyz/v1`  
**Результат:** 📱 **УЛЬТРА-МАЛЕНЬКАЯ МОДЕЛЬ**

#### Метрики:
- ⚠️ **Общая оценка:** 45.2% (D)
- ⚠️ **Успешность сценариев:** 28.6% (2/7)
- ❌ **Точность инструментов:** 10.0%
- ❌ **Полнота инструментов:** 14.3%
- ❌ **Семантическая точность:** 10.0%
- ✅ **Коэффициент избыточности:** x1.43 (приемлемо)

#### Особенности:
- **Бинарное поведение:** Либо 0 вызовов, либо много
- **Селективность:** Работает только с погодой и акциями
- **Специализация:** Хорошо в узких доменах
- **Размер vs качество:** Удивительно неплохо для такой маленькой модели

#### Успешные инструменты:
- ✅ `get_weather` (с избыточностью)
- ✅ `get_stock_price` (с избыточностью)
- ❌ `calculate`, планирование, конвертация

---

### 4. gpt-4.1

**API:** `http://193.233.114.29:4000/v1`  
**Результат:** ⚠️ **ПРОБЛЕМЫ С ИЗБЫТОЧНОСТЬЮ**

#### Метрики:
- ❌ **Общая оценка:** 30.2% (F)
- ⚠️ **Успешность сценариев:** 42.9% (3/7)
- ❌ **Точность инструментов:** 8.3%
- ⚠️ **Полнота инструментов:** 33.3%
- ❌ **Семантическая точность:** 8.3%
- ❌ **Коэффициент избыточности:** x4.00 (критично высокий)

#### Проблемы:
- **Избыточные вызовы:** 6 раз один инструмент для простых задач
- **Застревание:** Использует только один тип инструмента для сложных задач
- **Неэффективность:** Много "мусорных" вызовов

#### Примеры проблем:
```
simple_weather: get_weather(6) вместо get_weather(1)
travel_planning: get_weather(14) - только погода, никаких рейсов/отелей
```

---

### 5. Qwen/Qwen3-235B-A22B-Thinking-2507

**API:** `https://api.together.xyz/v1`  
**Результат:** ❌ **НЕПОДХОДЯЩАЯ АРХИТЕКТУРА**

#### Метрики:
- ❌ **Общая оценка:** 24.9% (F)
- ⚠️ **Успешность сценариев:** 42.9% (3/7)
- ❌ **Точность инструментов:** 6.2%
- ⚠️ **Полнота инструментов:** 33.3%
- ❌ **Семантическая точность:** 6.2%
- ❌ **Коэффициент избыточности:** x5.33 (экстремально высокий)

#### Критические проблемы:
- **Экстремальная избыточность:** До 28 вызовов одного инструмента
- **Худше меньшей модели:** Показала результат хуже Qwen3-Coder-480B
- **Thinking ≠ Tool Calling:** Архитектура для рассуждений не подходит для инструментов

---

### 6. zai-org/GLM-4.5-Air-FP8

**API:** `https://api.together.xyz/v1`  
**Результат:** 💀 **ПОЛНАЯ НЕСПОСОБНОСТЬ**

#### Метрики:
- ❌ **Все метрики:** 0.0%
- ❌ **Вызовы инструментов:** 0 из 21 ожидаемых
- ❌ **Понимание концепции:** Отсутствует

#### Проблема:
Модель отвечает на запросы, но никогда не вызывает инструменты. Полное отсутствие поддержки tool calling.

---

## 🎯 Выводы и рекомендации

### ✅ Для продакшн-систем:
1. **Qwen3-Coder-480B** - Лучший выбор для серьезных проектов
2. **g4ffree** - Потенциально отличный вариант после исправления API

### ⚠️ Для экспериментов:
3. **Gemma-3n-E4B** - Для ресурсо-ограниченных сред и простых задач

### ❌ Не рекомендуется:
4. **gpt-4.1** - Проблемы с избыточностью требуют доработки
5. **Qwen3-235B-Thinking** - Неподходящая архитектура
6. **GLM-4.5-Air-FP8** - Отсутствие поддержки tool calling

### 🔍 Ключевые инсайты:
- **Размер ≠ Качество:** Меньшая Coder-модель превосходит большую Thinking-модель
- **Специализация важна:** Модели для кодирования лучше справляются с инструментами
- **Квантизация критична:** FP8 может разрушить способности к tool calling
- **API совместимость:** Локальные проекты требуют адаптации форматов

---

## 📈 Подробная статистика по сценариям

### Успешные сценарии по моделям:

| Сценарий | Qwen3-Coder | g4ffree | Gemma-3n | gpt-4.1 | Qwen3-Thinking | GLM-4.5 |
|----------|-------------|---------|----------|---------|----------------|---------|
| simple_weather | ✅ | ❌* | ✅ | ✅ | ✅ | ❌ |
| simple_calculation | ✅ | ❌* | ❌ | ✅ | ✅ | ❌ |
| simple_stock | ✅ | ❌* | ✅ | ✅ | ✅ | ❌ |
| travel_planning | ✅ | ❌* | ❌ | ❌ | ❌ | ❌ |
| currency_travel | ✅ | ❌* | ❌ | ❌ | ❌ | ❌ |
| business_trip | ✅ | ❌* | ❌ | ❌ | ❌ | ❌ |
| investment_research | ✅ | ❌* | ❌ | ❌ | ❌ | ❌ |

*\* Технические ошибки API, но правильный выбор инструментов*

### Анализ избыточности вызовов:

| Модель | Ожидалось | Фактически | Коэффициент | Оценка |
|--------|-----------|------------|-------------|--------|
| **Qwen3-Coder-480B** | 21 | 20 | x0.95 | 🟢 Отлично |
| **g4ffree** | 21 | 9 | x0.43 | 🟡 Консервативно |
| **Gemma-3n-E4B** | 21 | 30 | x1.43 | 🟡 Приемлемо |
| **gpt-4.1** | 21 | 84 | x4.00 | 🔴 Критично |
| **Qwen3-Thinking** | 21 | 112 | x5.33 | 🔴 Экстремально |
| **GLM-4.5-Air** | 21 | 0 | x0.00 | 🔴 Нет поддержки |

### Время выполнения (среднее):

| Модель | Простые задачи | Сложные задачи | Общее время |
|--------|----------------|----------------|-------------|
| **Qwen3-Coder-480B** | 5.1s | 15.4s | 76.4s |
| **g4ffree** | 10.9s | 8.7s | 66.7s |
| **Gemma-3n-E4B** | 5.3s | 7.4s | 39.1s |
| **gpt-4.1** | 6.7s | 19.9s | 39.3s |
| **Qwen3-Thinking** | 6.9s | 24.6s | 118.9s |
| **GLM-4.5-Air** | 2.6s | 8.8s | 39.0s |

---

## 🔧 Технические детали

### API Endpoints протестированных моделей:

1. **Together.xyz API:**
   - `Qwen/Qwen3-Coder-480B-A35B-Instruct-FP8`
   - `Qwen/Qwen3-235B-A22B-Thinking-2507`
   - `google/gemma-3n-E4B-it`
   - `zai-org/GLM-4.5-Air-FP8`

2. **Локальный API:**
   - `http://193.233.114.29:4000/v1` (gpt-4.1)
   - `http://127.0.0.1:1337/v1` (g4ffree)

### Проблемы совместимости:

#### gpt-4.1 (LiteLLM):
- ✅ Поддержка tool calling
- ❌ Проблемы с избыточными вызовами
- ❌ Ошибки превышения лимита символов в сложных сценариях
- ⚠️ Проблемы с fallback моделями

#### g4ffree:
- ✅ Отличное понимание tool calling
- ❌ Ошибки `400 Bad Request` при выполнении
- ⚠️ Требует адаптации формата API

#### GLM-4.5-Air-FP8:
- ❌ Полное отсутствие поддержки tool calling
- ⚠️ Возможно, проблемы квантизации FP8

---

## 💡 Практические рекомендации

### Для разработчиков:

#### 🎯 Выбор модели по задачам:

**Простые задачи (1-2 инструмента):**
- 🥇 Qwen3-Coder-480B
- 🥈 g4ffree (после исправления API)
- 🥉 Gemma-3n-E4B (для ограниченных ресурсов)

**Сложные задачи (3+ инструментов):**
- 🥇 Qwen3-Coder-480B (единственный надежный вариант)

**Экспериментальные проекты:**
- g4ffree (высокий потенциал)
- Gemma-3n-E4B (для изучения поведения маленьких моделей)

#### 🔧 Настройки для проблемных моделей:

**gpt-4.1:**
```bash
--temperature 0.0  # Снизить случайность
--max-tools 3      # Ограничить количество инструментов
```

**Thinking модели:**
```bash
# Не рекомендуется для tool calling
# Лучше использовать для обычного чата
```

#### 📊 Мониторинг качества:

**Ключевые метрики для отслеживания:**
1. **Tool Precision** > 80%
2. **Tool Recall** > 70%
3. **Over/Under-call factor** < 1.5
4. **Execution Success Rate** = 100%

---

*Полный отчет создан на основе тестирования 6 LLM моделей
Дата: 24 августа 2025 года
Инструмент: LLM Tool Calling Test Suite V2*
