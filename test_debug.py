#!/usr/bin/env python3
"""
Debug tool to see raw API responses
"""

import json
import time
from openai import OpenAI

def test_tool_response(api_base, api_key, model):
    client = OpenAI(api_key=api_key, base_url=api_base)
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "calculate",
                "description": "Perform a calculation",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "expression": {"type": "string"}
                    },
                    "required": ["expression"]
                }
            }
        }
    ]
    
    messages = [
        {"role": "user", "content": "What is 847 divided by 6?"}
    ]
    
    print("Sending request...")
    print(f"Model: {model}")
    print(f"Messages: {messages}")
    print("\n" + "="*50)
    
    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            tools=tools,
            tool_choice="auto",
            temperature=0.1,
            max_tokens=150
        )
        
        print("RAW RESPONSE:")
        print(response)
        print("\n" + "="*50)
        
        if response.choices:
            msg = response.choices[0].message
            print("\nMESSAGE DETAILS:")
            print(f"Type: {type(msg)}")
            print(f"Content: {msg.content}")
            print(f"Has tool_calls: {hasattr(msg, 'tool_calls')}")
            if hasattr(msg, 'tool_calls'):
                print(f"Tool calls: {msg.tool_calls}")
                if msg.tool_calls:
                    for tc in msg.tool_calls:
                        print(f"\nTool Call Details:")
                        print(f"  ID: {tc.id}")
                        print(f"  Type: {tc.type}")
                        print(f"  Function name: {tc.function.name}")
                        print(f"  Arguments: {tc.function.arguments}")
            
            # Check for any special tokens
            if msg.content:
                if "<|" in msg.content or "|>" in msg.content:
                    print("\nWARNING: Special tokens detected in content!")
                    print(f"Content repr: {repr(msg.content)}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 4:
        print("Usage: python test_debug.py <api_base> <api_key> <model>")
        sys.exit(1)
    
    test_tool_response(sys.argv[1], sys.argv[2], sys.argv[3])