
============================================================
COHERE TOOL CALLING TEST REPORT
============================================================
Model: ccommand-r7b-arabic-02-2025
Timestamp: 2025-08-24 20:04:14

OVERALL SUMMARY
----------------------------------------
Total Scenarios: 5
Successful Scenarios: 0
Failed Scenarios: 5
Scenario Success Rate: 0.0%

TOOL CALL STATISTICS
-------------------------
Total Tool Calls: 0
Average Execution Time: 0.31s

SCENARIO DETAILS
----------------------------------------

  [FAIL] simple_get_weather
    Tool calls: 0
    Execution time: 0.57s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': '3d48a18983dbd4a771f35b09b6d2b09a', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '3', 'date': 'Sun, 24 Aug 2025 17:04:08 GMT', 'content-length': '183', 'x-envoy-upstream-service-time': '49', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'id': '36e061cf-f49a-4192-9381-243bed70cc2c', 'message': "model 'ccommand-r7b-arabic-02-2025' not found, make sure the correct model ID was used and that you have access to the model."}

  [FAIL] simple_calculate
    Tool calls: 0
    Execution time: 0.24s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': 'e6b815af09d3d1292713110a15781c76', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '2', 'date': 'Sun, 24 Aug 2025 17:04:10 GMT', 'content-length': '183', 'x-envoy-upstream-service-time': '7', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'id': '09398448-4b09-403d-8e5e-57160ddf34cf', 'message': "model 'ccommand-r7b-arabic-02-2025' not found, make sure the correct model ID was used and that you have access to the model."}

  [FAIL] simple_get_stock_price
    Tool calls: 0
    Execution time: 0.25s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': 'd104c2b504d5936a3971bb1974a8d08c', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '1', 'date': 'Sun, 24 Aug 2025 17:04:11 GMT', 'content-length': '183', 'x-envoy-upstream-service-time': '6', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'id': '032855cf-0b22-441e-9cc1-a33fa074055e', 'message': "model 'ccommand-r7b-arabic-02-2025' not found, make sure the correct model ID was used and that you have access to the model."}

  [FAIL] simple_search_flights
    Tool calls: 0
    Execution time: 0.28s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': '331e18468bb9ee352a96a278b713d368', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '0', 'date': 'Sun, 24 Aug 2025 17:04:12 GMT', 'content-length': '183', 'x-envoy-upstream-service-time': '33', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 404, body: {'id': '61de5433-1232-4ffb-a349-6988e0f53d8e', 'message': "model 'ccommand-r7b-arabic-02-2025' not found, make sure the correct model ID was used and that you have access to the model."}

  [FAIL] simple_convert_currency
    Tool calls: 0
    Execution time: 0.23s
    Error: headers: {'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': '7e2354e6883e9ed6c93d39c7824a60f5', 'date': 'Sun, 24 Aug 2025 17:04:13 GMT', 'content-length': '372', 'x-envoy-upstream-service-time': '3', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 429, body: {'id': '4e52e77a-1a32-4fdf-bb2f-8a2b6c709380', 'message': "You are using a Trial key, which is limited to 10 API calls / minute. You can continue to use the Trial key for free or upgrade to a Production key with higher rate limits at 'https://dashboard.cohere.com/api-keys'. Contact us on 'https://discord.gg/XW44jPfYJu' or email <NAME_EMAIL> with any questions"}

============================================================
FINAL SCORE: 0.0% (F)
============================================================
